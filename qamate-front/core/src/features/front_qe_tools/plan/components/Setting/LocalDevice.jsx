import {Form, Select, Tag} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';

function LocalDevice({deviceList, osType, disabled = false}) {
    return (
        <Form.Item
            label='执行设备'
            name="localDevice"
            rules={
                [
                    {
                        required: true,
                        message: '请选择执行设备',
                    },
                ]}
        >
            <Select
                mode="multiple"
                style={{width: '100%'}}
                disabled={disabled}
                placeholder='请选择执行设备'
            >
                {
                    deviceList[[+osType === 2 ? 'iOS' : 'android']]?.map(
                        (device) => {
                            let deviceStatus;
                            let deviceStatusColor;
                            switch (device.status) {
                                case 0:
                                    deviceStatus = '已连接';
                                    deviceStatusColor = '#1677ff';
                                    break;
                                case 1:
                                    deviceStatus = '初始化';
                                    deviceStatusColor = '#1677ff';
                                    break;
                                case 2:
                                    deviceStatus = '空闲';
                                    deviceStatusColor = '#73d13d';
                                    break;
                                case 3:
                                    deviceStatus = '繁忙';
                                    deviceStatusColor = '#faad14';
                                    break;
                                case 4:
                                    deviceStatus = '异常';
                                    deviceStatusColor = '#ff4d4f';
                                    break;
                                case 5:
                                    deviceStatus = '离线';
                                    deviceStatusColor = '#bfbfbf';
                                    break;
                                case 6:
                                    deviceStatus = '离线中';
                                    deviceStatusColor = '#bfbfbf';
                                    break;
                                default:
                                    break;
                            }
                            return (
                                <Select.Option
                                    key={device?.phoneId}
                                    value={device?.phoneId}
                                >
                                    <Tag color={deviceStatusColor}>{deviceStatus}</Tag>
                                    <span>{`${device.marketName} - ${device.deviceId}`}</span>
                                </Select.Option>
                            );
                        }
                    )}
            </Select>
        </Form.Item>
    );
};

export default connectModel([baseModel], state => ({
    deviceList: state.common.base.deviceList,
    currentSpace: state.common.base.currentSpace,
}))(LocalDevice);
