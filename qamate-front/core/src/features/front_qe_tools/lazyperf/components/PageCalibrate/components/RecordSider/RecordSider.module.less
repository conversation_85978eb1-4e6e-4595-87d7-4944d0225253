.collapseButton {
    position: fixed;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    z-index: 1001;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        background: #f5f5f5;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .anticon {
        font-size: 16px;
        color: #666;
    }
}

.recordSider {
    position: fixed;
    top: 0;
    left: 0;
    width: 80px;
    height: 100vh;
    background: #fff;
    border-right: 1px solid #d9d9d9;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.recordMenu {
    height: 100%;
    overflow-y: auto;
    padding-top: 60px;
    border-right: none;

    :global(.ant-menu-item) {
        text-align: center;
        margin: 0;
        padding: 12px 0;
        height: auto;
        line-height: 1;

        &:hover {
            background-color: #f5f5f5;
        }

        &.ant-menu-item-selected {
            background-color: #e6f7ff;
            border-right: 3px solid #1890ff;
        }
    }

    :global(.ant-menu-item-selected::after) {
        display: none;
    }
}
