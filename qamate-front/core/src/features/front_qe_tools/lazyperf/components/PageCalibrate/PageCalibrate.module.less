.pageCalibrate {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    
         // 顶部操作栏
        .topBar {
            width: calc(100% - 300px);
            position: absolute;
            top: -45px;
            height: 48px;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            flex-shrink: 0;
            .leftInfo {
                display: flex;
                align-items: center;
                gap: 16px;

                .calibrateTitle {
                    font-weight: 500;
                    font-size: 14px;
                    color: #262626;
                }

                .ttiInfo {
                    font-size: 14px;
                    color: #595959;
                }
            }

            .rightActions {
                display: flex;
                align-items: center;
            }
        }
    .recordSider {
        width: 80px;
        height: 100%;
        background: #fff;
        border-right: 1px solid #d9d9d9;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
    }

    .recordMenu {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;
    }

    .recordItem {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        margin: 0;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
            background-color: #f5f5f5;
        }

        &.active {
            background-color: #e6f7ff;
            border-right: 3px solid #1890ff;
            color: #1890ff;
        }

        // 记录项文本样式
        .recordText {
            &.invalid {
                color: #ff4d4f;
                text-decoration: line-through;
            }
        }
    }

    .siderPagination {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 80px;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #fff;
        text-align: center;

        .paginationBtn {
            width: 80px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            background: #fff;

            &:hover:not(.disabled) {
                background-color: #f5f5f5;
            }

            &.disabled {
                color: #d9d9d9;
                cursor: not-allowed;
            }
        }

        .paginationInfo {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 2px 0;

            .currentPage {
                width: 50px;
                padding: 0;
                text-align: center;
                border: 1px solid #d9d9d9;
                border-radius: 2px;
                font-size: 12px;
                line-height: 20px;
                background: #fff;
            }

            .totalInfo {
                width: 80px;
                font-size: 12px;
                text-align: center;
                margin-top: 4px;
                color: #666;
            }
        }
    }

    .mainContent {
        flex: 1;
        height: 100%;
        overflow: hidden;

       

        // 帧校准组件容器
        .frameViewerContainer {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 16px;
            min-height: 700px; // 确保有足够的高度显示首尾帧
        }


    }

    .emptyState {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 400px;
        color: #999;
        font-size: 16px;

        p {
            margin: 8px 0;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .recordInfo {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 14px;

            .calibrateTag {
                background: #52c41a;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }

            .ttiInfo {
                font-size: 16px;
                font-weight: 600;
                color: #333;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    background: #1890ff;
                    border-radius: 50%;
                    margin-right: 8px;
                    vertical-align: middle;
                }

                // TTI错误状态样式
                &.error {
                    color: #ff0000;
                }
            }

            .invalidTag {
                background: #ff4d4f;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
            }

            .invalidComment {
                color: #666;
                font-size: 12px;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .pagination {
        margin-top: auto;
        padding-top: 16px;
        text-align: center;
        border-top: 1px solid #f0f0f0;

        :global(.ant-pagination) {
            display: flex;
            justify-content: center;
        }
    }
}
