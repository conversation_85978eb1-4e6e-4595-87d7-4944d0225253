import { post } from 'COMMON/utils/requestUtils';

// 创建性能评测计划
export const createPerfPlanList = (params) => {
    return post('/core/perf/plan/create', params);
};

// 获取性能评测任务信息
export const getPerfPlanList = (params) => {
    return post('/core/perf/plan/list', params);
};

// 删除性能评测
export const deletePerfPlan = (params) => {
    return post('/core/perf/plan/delete', params);
};

// 追加性能评测次数
export const appendPerfPlan = (params) => {
    return post('/core/perf/plan/append', params);
};

// 取消性能评测
export const cancelPerfPlan = (params) => {
    return post('/core/perf/plan/cancel', params);
};

// 获取实际执行列表任务信息
export const getSpeedRoundList = (params) => {
    return post('/core/perf/speed/round/list', params);
};

// 更新速度评测数据有效情况
export const updateSpeedRound = (params) => {
    return post('/core/perf/speed/round/valid', params);
};

// 计算数据评测报告任务信息
export const getSpeedRoundReport = (params) => {
    return post('/core/perf/speed/round/report', params);
};
// 人工校准速度评测数据
export const updateRecordCorrect = (params) => {
    return post('/core/perf/speed/record/correct', params);
};
