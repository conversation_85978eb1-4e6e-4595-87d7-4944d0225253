@import "RESOURCES/css/common.less";

.noContent {
    margin-top: 200px;
}

.layoutSider {
    position: relative;
    height: calc(100vh - 40px);
    background-color: var(--layout-background-color);
    background-image: linear-gradient(180deg, var(--layout-background-color), #fefeff);
    z-index: 99;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    :global {

        .ant-tree,
        .ant-menu {
            background-color: transparent;
        }

        .ant-tree-switcher {
            width: 0px;
            line-height: 30px !important;
        }

        .ant-tree-treenode {
            padding: 0 10px 5px 0;
        }

        .ant-tree .ant-tree-treenode-draggable {
            cursor: pointer !important;
        }

    }
}

.siderHeader {
    height: 30px;
    line-height: 30px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .groupInfo {
        flex: 1;
        overflow: hidden;
    }

    .searchStyle {
        flex: 1;
        margin: 5px 0 0 8px;
    }

    .iconGroup {
        display: flex;
        align-items: center;
        gap: 4px;
        margin: 3px 13px 0 0;
    }

    .addIcon {
        text-align: center;
        font-size: 14px;
        color: var(--color2);
    }
}

.iconWrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.iconWrapper:hover {
    background-color: rgba(0, 0, 0, 0.1);
    /* 你可以根据需要调整颜色 */
}

// 新增
.addCaseText {
    font-size: 12px;
}

.btnPos {
    float: right;
}

.operatorIcon {
    color: var(--color2);
    padding-right: 8px;
    font-size: 14px;
}

.delIcon {
    color: var(--error-color);
    padding-right: 8px;
    font-size: 12px;
}

.flexCenterX {
    display: flex;
    align-items: center;
    font-size: 12px;
}

.selectOption {
    margin-bottom: 5px;
}

.title {
    display: inline-block;
    width: 80px;
    font-size: 14px;
}

.groupInfo {
    padding-left: 12px;
    font-size: 12px;
    font-weight: bold;
    cursor: default;

    .text {
        margin-left: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.contentArea {
    flex: 1;
    overflow-y: auto;
    position: relative;
    min-height: 0; /* 确保flex子元素能正确收缩 */

    &.hasPagination {
        padding-bottom: 40px;
    }

    /* 确保Spin组件填充整个高度 */
    :global {
        .ant-spin-container {
            height: 100%;
            min-height: calc(100vh - 200px); /* 给一个最小高度 */
        }
    }
}

.paginationWrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 38px;
    padding: 8px 12px;
    background: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(255,255,255,1));
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(4px);
    z-index: 10;
}