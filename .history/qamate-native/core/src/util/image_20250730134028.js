/**
 * @create 王家麒@2022.08.10
 */
const axios = require('axios');
const fs = require('fs');
const sharp = require('sharp');
const childProcess = require('child_process');
const { v4: uuidv4 } = require('uuid');
const { time: { currentTimestamp }, shell: { getPath } } = require('@baidu/bat-util');
const { CACHE: { CACHE_DEVICE_SCREENSHOT_DIR } } = require('../config');

/**
 * 图像裁切。
 *
 * @param {Buffer} imageBuffer
 * @param {Object} bounds
 * @return {Promise{Buffer}}
 */
const extractImage = async (imageBuffer, { x, y, width, height }) => {
    // 进 1
    let ceilX = Math.ceil(x);
    let ceilY = Math.ceil(y);
    if (ceilX > x) {
        width = width - (ceilX - x);
    }
    if (ceilY > y) {
        height = height - (ceilY - y);
    }

    // 去掉小数部分
    let floorWidth = Math.floor(width);
    let floorHeight = Math.floor(height);
    if (floorWidth < 1 || floorHeight < 1) {
        throw new Error(`从图像中裁切的矩形框高度及宽度须不小于 1，传入：height=${height} width=${width}`);
    }
    let res = await sharp(imageBuffer).extract({
        left: ceilX,
        top: ceilY,
        width: floorWidth,
        height: floorHeight
    }).jpeg().toBuffer();

    return res;
};

/**
 * 图像画框
 *
 * @param {Buffer} imageBuffer
 * @param {Object} bounds
 * @param {number} borderSize
 * @return {Promise{Buffer}}
 */
const drawBorder = async (imageBuffer, { x, y, width, height }, borderSize = 5) => {
    // 进 1
    let ceilX = Math.ceil(x);
    let ceilY = Math.ceil(y);
    if (ceilX > x) {
        width = width - (ceilX - x);
    }
    if (ceilY > y) {
        height = height - (ceilY - y);
    }

    // 去掉小数部分
    let floorWidth = Math.floor(width);
    let floorHeight = Math.floor(height);
    if (floorWidth < 1 || floorHeight < 1) {
        throw new Error(`从图像中画红框的矩形框高度及宽度须不小于 1，传入：height=${height} width=${width}`);
    }

    x = ceilX;
    y = ceilY;
    width = floorWidth;
    height = floorHeight;

    let sharpImage = await sharp(imageBuffer);
    let metadata = await sharpImage.metadata();

    let borderHeight = height;
    if (y + height > metadata.height) {
        borderHeight = metadata.height - y;
    }

    let borderWidth = width;
    if (x + width > metadata.width) {
        borderWidth = metadata.width - x;
    }

    let widthBorder = await getBorder(borderWidth, borderSize);
    let heightBorder = await getBorder(borderSize, borderHeight);

    let res = await sharpImage.composite([
        {
            input: widthBorder,
            left: x,
            top: y
        },
        {
            input: widthBorder,
            left: x,
            top: y + height - borderSize
        },
        {
            input: heightBorder,
            left: x,
            top: y
        },
        {
            input: heightBorder,
            left: x + width - borderSize,
            top: y
        }
    ]).png().toBuffer();
    return res;
};

// 放进缓存, 会删除的
const cacheScreenshot = (imageBuf, fileName, ext = 'jpg') => {
    if (!fileName) {
        throw new Error('fileName 不得为空');
    }
    let path = `${CACHE_DEVICE_SCREENSHOT_DIR}/${fileName}.${ext}`;
    fs.writeFileSync(path, imageBuf);
    return path;
};

const cacheScreenshotForRandomFile = (imageBuf, ext = 'jpg') => {
    let path = `${CACHE_DEVICE_SCREENSHOT_DIR}/${currentTimestamp()}_${uuidv4()}.${ext}`;
    fs.writeFileSync(path, imageBuf);
    return path;
};

const cacheDeviceScreenshot = (deviceId, imageBuf, ext = 'jpg') => {
    if (!deviceId) {
        throw new Error('deviceId 不得为空');
    }
    let path = `${CACHE_DEVICE_SCREENSHOT_DIR}/${deviceId}-${currentTimestamp()}.${ext}`;
    fs.writeFileSync(path, imageBuf);
    return path;
};

const getScreenshot = async (screenshot, prefix, worker) => {
    if (screenshot.startsWith('http')) {
        let { isCache, link } = await worker.callFront('file.getFileCacheData', { bosLink: screenshot });
        if (isCache) {
            screenshot = link;
        }
    }
    if (screenshot.startsWith('file')) {
        screenshot = screenshot.slice(7);
    }
    else {
        screenshot = await decodeWebBase64(screenshot);
        screenshot = cacheScreenshot(screenshot, `_${prefix}_${currentTimestamp()}`);
    }
    return screenshot;
};

const ffmpeg = async (videoFilePath, storeDir, startTime) => {
    startTime = parseInt(startTime, 10);
    return await new Promise((resolve, reject) => {
        try {
            // 执行分帧
            let subProcess = childProcess.spawn(
                `ffmpeg -i '${videoFilePath}' -fpsmax 60 ${storeDir}/video_%d.jpg`,
                {
                    shell: true,
                    env: { PATH: getPath() }
                }
            );

            // 处理帧间隔
            let frameDuration = 0;
            const getDuration = (res) => {
                for (let line of res.split('\n')) {
                    if (-1 !== line.indexOf('fps,')) {
                        let lineSplit = line.split(', ');
                        for (let splitItem of lineSplit) {
                            if (-1 !== splitItem.indexOf('fps')) {
                                splitItem.slice(0, splitItem - 4);
                                let fps = parseFloat(splitItem, 10);
                                frameDuration = 1000 / fps;
                            }
                        }
                    }
                }
            };
            subProcess.stdout.on('data', res => {
                getDuration(res.toString());
            });
            subProcess.stderr.on('data', res => {
                getDuration(res.toString());
            });

            // 处理分帧结果
            subProcess.on('close', code => {
                // 处理失败结果
                if (0 !== code) {
                    let err = new Error(`ffmpeg 退出代号 ${code}`);
                    return reject(err);
                }
                fs.unlinkSync(videoFilePath);
                // 对有效帧图进行重命名
                let files = fs.readdirSync(storeDir);
                let frames = [];
                for (let [fileIndex, fileName] of files.entries()) {
                    if (!fileName.endsWith('.jpg')) {
                        continue;
                    }
                    let index = fileName.slice(6, fileName.length - 4);
                    let timestamp = startTime + parseInt(((index - 1) * frameDuration).toFixed(2), 10);
                    let newFileName = `${fileIndex}_${timestamp}.jpg`;
                    let newFilePath = `${storeDir}/${newFileName}`;
                    fs.renameSync(`${storeDir}/${fileName}`, newFilePath);
                    frames.push({ timestamp, fileName: newFileName });
                }
                if (0 === frames.length) {
                    let err = new Error('ffmpeg 产出帧数为 0');
                    return reject(err);
                }
                // 按照时间进行排序
                frames.sort(function (f1, f2) {
                    return f1.timestamp - f2.timestamp;
                });
                return resolve(frames);
            });
        }
        catch (err) {
            reject(err);
        }
    });
};

const mergeVideo = async ({ imageList, storeDir, needClear = false }) => {
    if (imageList.length < 2) {
        throw new Error('图片列表长度必须大于等于 2');
    }
    let fps = Math.round(
        1000 /
        ((imageList[imageList.length - 1].timestamp - imageList[0].timestamp) / (imageList.length - 1))
    );
    for (let [imageIndex, imageItem] of imageList.entries()) {
        fs.renameSync(`${storeDir}/${imageItem.fileName}`, `${storeDir}/${(imageIndex + 1)}.jpg`);
        imageItem.newPath = `${storeDir}/${(imageIndex + 1)}.jpg`;
    }
    return await new Promise((resolve, reject) => {
        try {
            let fileName = `${currentTimestamp()}_${uuidv4()}.mp4`;
            // 执行合并算法
            let subProcess = childProcess.spawn(
                `ffmpeg -r ${fps} -f image2 -i ${storeDir}/%d.jpg -vf "scale=trunc(iw/2)*2:trunc(ih/2)*2" ${storeDir}/${fileName}`,
                {
                    shell: true,
                    env: { PATH: getPath() }
                }
            );

            subProcess.stdout.on('data', res => { });
            subProcess.stderr.on('data', res => { });

            // 处理合并结果
            subProcess.on('close', code => {
                // 处理失败结果
                if (0 !== code) {
                    let err = new Error(`ffmpeg 退出代号 ${code}`);
                    return reject(err);
                }
                if (needClear) {
                    try {
                        for (let imageItem of imageList) {
                            fs.unlinkSync(imageItem.newPath);
                        }
                    }
                    catch { }
                }
                return resolve(`${storeDir}/${fileName}`);
            });
        }
        catch (err) {
            reject(err);
        }
    });
};

const encodeWebBase64 = screenshot => {
    return `data:image/png;base64,${screenshot.toString('base64')}`;
};

const decodeWebBase64 = async screenshot => {
    if (Buffer.isBuffer(screenshot)) {
        return screenshot;
    }
    // 云端图片
    if (screenshot.startsWith('http')) {
        let response = null;
        for (let i = 0; i < 3; i++) {
            try {
                response = await axios.get(screenshot, {
                    responseType: 'arraybuffer',
                    timeout: 20000
                });
                break;
            } catch { }
        }
        if (null === response) {
            response = await axios.get(screenshot, {
                responseType: 'arraybuffer'
            });
        }
        screenshot = Buffer.from(response.data, 'binary');
    }
    // data:image/png;base64 格式图片
    else if (screenshot.startsWith('data')) {
        screenshot = Buffer.from(screenshot.slice(22), 'base64');
    }
    else if ('string' === typeof screenshot) {
        // 处理一下 file 路径文件
        if (screenshot.startsWith('file')) {
            screenshot = screenshot.slice(7);
        }
        // 本地文件路径
        if (fs.existsSync(screenshot)) {
            screenshot = fs.readFileSync(screenshot);
        }
        // 纯 base64 结构图片
        else {
            screenshot = Buffer.from(screenshot, 'base64');
        }
    }
    return screenshot;
};

const getImageBorder = async screenshot => {
    screenshot = await decodeWebBase64(screenshot);
    let sharpImage = await sharp(screenshot).metadata();
    return {
        height: sharpImage.height,
        width: sharpImage.width
    };
};

module.exports = {
    extractImage,
    drawBorder,
    cacheScreenshot,
    cacheScreenshotForRandomFile,
    cacheDeviceScreenshot,
    getScreenshot,
    ffmpeg,
    mergeVideo,
    encodeWebBase64,
    decodeWebBase64,
    getImageBorder
};