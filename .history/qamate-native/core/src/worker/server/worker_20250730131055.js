/**
 * @create 王家麒@2022.08.08
 */
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const getPort = require('get-port');
const childProcess = require('child_process');
const { v4: uuidv4 } = require('uuid');
const {
    file: { mkdir },
    time: { currentTimestamp, delay },
    shell: { killProcess },
    error: { JsonrpcError }
} = require('@baidu/bat-util');

const LazyBaseWorker = require('../../lib/lazy-worker/LazyBaseWorker');
const { clearUnuseCache } = require('./worker/clear');
const { checkPortUsable } = require('../../util/network');
const { cloudServerRequest } = require('../../util/request');
const { getScreenshot, cacheScreenshotForRandomFile } = require('../../util/image');
const { uploadFromFile } = require('../../util/bos/index');
const { NEW_VRSION, vesionStringCopare } = require('../../util/version');
const {
    IS_DEBUG,
    IS_LOCALTOOL,
    SERVER_DIR,
    PROFILE: { SERVER_PROFILE_DIR, SYS_PROFILE_DIR }
} = require('../../config');
const { logger } = require('./logger');

const MAX_REQUEST_QPS = 3;

let worker = null;

const getLogTag = method => `[localServer.${method}]`;

class LocalServerWorker extends LazyBaseWorker {
    constructor({ name, fatherName }, { resourcePath }) {
        super({ name, fatherName, logger });

        this.script = '';
        this.serverProfile = '';
        this.resourcePath = resourcePath;

        this.status = 0; // 0 未启动 1 启动中 2 运行中 3 已关闭
        this.isReadyStart = false;
        this.activeClose = false;
        this.serverMsgId = 0;
        this.port = 9966;
        this.process = null;

        this.lazyId = null;
        this.requestTimes = MAX_REQUEST_QPS;

        // 注册路由
        this.controller = Object.assign({
            'start': 'start',
            'restart': 'restart',
            'sendToServer': 'sendToServer',
            'getServerPort': 'getServerPort',
        }, this.controller);
    }

    async closeWorker(code) {
        try {
            if (this.process && this.process.kill) {
                let signal = 'SIGHUP';
                if (0 !== code) {
                    signal = 'SIGINT';
                }
                this.activeClose = true;
                await killProcess(this.process, signal);
            }
        }
        catch { }
        finally {
            await super.closeWorker(code);
        }
    }

    async restart() {
        if (!IS_LOCALTOOL) {
            this.logger.warn(`${getLogTag('restart')} CMD 模式下不支持重启策略服务`);
            return;
        }
        this.activeClose = true;
        // 如果策略服务在启动中，不能让他向下走，他会启动两个进程
        while (1 === this.status) {
            await delay(100);
        }
        if (this.process && this.process.kill) {
            await killProcess(this.process, 'SIGINT');
            this.process = null;
            await delay(100);
        }
        this.activeClose = false;
        await this.start();
    }

    async initPath() {
        if (IS_DEBUG) {
            this.script = path.join(__dirname, '../../../../../qamate-strategy/core', 'main.py');
            this.serverProfile = path.join(__dirname, '../../../../../qamate-strategy/core', 'profile.json');
            this.isReadyStart = true;
            return;
        }
        const getPackageModelPath = async () => {
            try {
                const serverPath = path.join(this.resourcePath, 'public/server');
                const serverModelPath = path.join(this.resourcePath, 'public/model');
                const serverAgentPath = path.join(this.resourcePath, 'public/agent');
                let versionInfo = {};
                if (fs.existsSync(serverPath)) {
                    versionInfo.server = {
                        'version': JSON.parse(fs.readFileSync(`${serverPath}/version.json`)).version,
                        'path': `${serverPath}/output`
                    };
                }
                else {
                    worker.logger.info(`${getLogTag('getPackageModelPath')} 包内无策略服务`);
                }
                if (fs.existsSync(serverAgentPath)) {
                    versionInfo.server_agent = {
                        'version': JSON.parse(fs.readFileSync(`${serverAgentPath}/version.json`)).version,
                        'path': `${serverAgentPath}/output`
                    };
                }
                else {
                    worker.logger.info(`${getLogTag('getPackageModelPath')} 包内无 Agent`);
                }
                if (fs.existsSync(serverModelPath)) {
                    for (let modelName of fs.readdirSync(serverModelPath)) {
                        versionInfo[modelName] = {
                            'version': JSON.parse(
                                fs.readFileSync(`${serverModelPath}/${modelName}/version.json`)
                            ).version,
                            'path': `${serverModelPath}/${modelName}/output`
                        };
                    }
                }
                else {
                    worker.logger.info(`${getLogTag('getPackageModelPath')} 包内无模型`);
                }
                return versionInfo;
            }
            catch (err) {
                worker.logger.warn(`${getLogTag('getPackageModelPath')} 获取包内信息失败 ${err.stack}`);
                return {};
            }
        };
        const getServerBootProfilePath = async () => {
            try {
                if (!fs.existsSync(`${SERVER_PROFILE_DIR}/server.json`)) {
                    fs.writeFileSync(`${SERVER_PROFILE_DIR}/server.json`, '{}');
                }
                let profileVersion = JSON.parse(fs.readFileSync(`${SERVER_PROFILE_DIR}/server.json`));
                let newProfile = {};
                for (let key in profileVersion) {
                    if (fs.existsSync(profileVersion[key].path)) {
                        newProfile[key] = profileVersion[key];
                    }
                    else {
                        worker.logger.warn(`${getLogTag('getServerBootProfilePath')} 配置中 ${key} 文件不存在`);
                    }
                }
                return newProfile;
            }
            catch (err) {
                worker.logger.warn(`${getLogTag('getServerBootProfilePath')} 获取配置的启动信息失败 ${err.stack}`);
                return {};
            }
        };
        // 对比本地版本和配置的启动版本，如果本地版本比配置的启动版本高，则使用本地版本
        let packageVersion = await getPackageModelPath();
        worker.logger.info(`${getLogTag('getServerBootProfilePath')} 包内版本信息 ${JSON.stringify(packageVersion)}`);
        let profileVersion = await getServerBootProfilePath();
        worker.logger.info(`${getLogTag('getServerBootProfilePath')} 配置版本信息 ${JSON.stringify(profileVersion)}`);
        // 对比工具启动和配置启动版本，如果工具版本更高则复制内容到工具
        let needUpdate = false;
        for (let key in packageVersion) {
            const copyServer = async oldPath => {
                let targetDir = `${SERVER_DIR}/${currentTimestamp()}_${uuidv4()}`;
                mkdir(targetDir);
                if (oldPath.endsWith('output')) {
                    oldPath = oldPath.split('/').slice(0, -1).join('/');
                }
                fs.cpSync(oldPath, targetDir, { recursive: true, dereference: true });
                return `${targetDir}/output`;
            };
            // 如果配置中压根没有这个包版本，则强制复制获取
            // 如果包中版本比配置中版本高，则强制复制获取
            if (
                undefined === profileVersion[key] ||
                NEW_VRSION === vesionStringCopare(profileVersion[key].version, packageVersion[key].version)
            ) {
                needUpdate = true;
                profileVersion[key] = {
                    version: packageVersion[key].version,
                    path: await copyServer(packageVersion[key].path)
                };
                worker.logger.info(
                    `${getLogTag('getServerBootProfilePath')} ${key} 内容从包内复制，位置: ${profileVersion[key].path}`);
            }
        }
        // 强制兜底一个策略地址
        if (undefined === profileVersion.server) {
            needUpdate = true;
            profileVersion.server = {
                version: '0.0.0',
                path: ''
            };
        }
        // 有变化的话写入配置文件
        if (needUpdate) {
            fs.writeFileSync(`${SERVER_PROFILE_DIR}/server.json`, JSON.stringify(profileVersion));
        }

        worker.logger.info(`${getLogTag('getServerBootProfilePath')} 最终启动信息 ${JSON.stringify(profileVersion)}`);
        this.script = path.join(profileVersion.server.path, 'py/main', 'main');
        this.serverProfile = `${SERVER_PROFILE_DIR}/server.json`;
        this.isReadyStart = true;
    }

    getServerPort() {
        return this.port;
    }

    async start(timeout = 180000) {
        if (!IS_LOCALTOOL) {
            this.logger.warn(`${getLogTag('restart')} CMD 模式下不支持重启策略服务`);
            return;
        }
        const tag = getLogTag('start');
        if (null === this.process) {
            return new Promise(async (resolve, reject) => {
                let startTime = currentTimestamp();
                // 找一个有效的端口
                let portUsable = await checkPortUsable(this.port);
                if (!portUsable) {
                    this.port = await getPort();
                }
                this.status = 1;
                // 启动子进程
                this.logger.info(
                    `${tag} localserver 准备启动` +
                    ` 文件: ${this.script} 端口: ${this.port} 配置: ${this.serverProfile}`
                );
                if (IS_DEBUG) {
                    this.process = childProcess.spawn(
                        `${path.join(__dirname, '../../../../../qamate-strategy/core/venv/bin', 'python3.9')}` +
                        ` ${this.script} -p ${this.port} -c ${this.serverProfile} &> /dev/null`,
                        {
                            shell: true
                        }
                    );
                }
                else {
                    this.process = childProcess.spawn(
                        `${this.script} -p ${this.port} -c ${this.serverProfile} &> /dev/null`,
                        {
                            shell: true
                        }
                    );
                }

                // 监听启动失败
                this.process.once('close', async (code, signal) => {
                    const tag = getLogTag('close');
                    let oriStatus = this.status;
                    this.status = 3;
                    this.process = null;
                    // 如果不是主动关闭, 且不是启动中的状态
                    if (false === this.activeClose && 2 === oriStatus) {
                        this.logger.error(`${tag} localserver 遇到 ${signal} 尝试重启一次`);
                        try {
                            await this.start();
                            this.logger.info(`${tag} localserver 尝试重启成功`);
                        }
                        catch (err) {
                            this.logger.error(`${tag} localserver 尝试重启失败 ${err.stack}`);
                        }
                    }
                    else {
                        this.logger.info(`${tag} ${code}, ${signal}`);
                    }
                    reject(new Error(`${this.name} 初始化失败, code: ${code}`));
                });

                this.process.stdout.on('data', res => { });
                this.process.stderr.on('data', res => { });

                // 监听启动成功
                let expiredTime = currentTimestamp() + timeout;
                while (currentTimestamp() < expiredTime) {
                    await delay(500);
                    let isStart = await this.sendToServer('status').catch(() => false);
                    if (isStart) {
                        this.status = 2;
                        if (this.process) {
                            this.logger.info(
                                `${tag} 启动成功; pid: ${this.process.pid};端口: ${this.port}; ` +
                                `调试模式: ${IS_DEBUG}; 耗时 ${currentTimestamp() - startTime}ms`
                            );
                        }
                        clearUnuseCache(this);
                        return resolve(true);
                    }
                }
                this.status = 3;
                reject(new Error(`${this.name} 初始化超时`));
            });
        }
    }

    async handlePicUrl({ paramsList = [], type = 'up' }) {
        const handleUrlByType = async picUrl => {
            if ('up' === type) {
                if (fs.existsSync(picUrl)) {
                    return await uploadFromFile(
                        picUrl,
                        `/lazycloud/${currentTimestamp()}-${uuidv4()}.jpg`,
                        { retryCount: 3 },
                        { timeout: 180000 }
                    );
                }
            }
            else if ('down' === type) {
                if (picUrl.startsWith('http')) {
                    return await getScreenshot(picUrl, 'handlePicUrl', this);
                }
            }
            return picUrl;
        };
        for (let param of paramsList) {
            // 判断是否是 Map 对象
            if ('object' === typeof param && !Array.isArray(param)) {
                if (undefined !== param.module_info) {
                    for (let key in param.module_info) {
                        if (['image_path', 'case_image_path', 'current_img'].includes(key)) {
                            param.module_info[key] = await handleUrlByType(param.module_info[key]);
                        }
                    }
                }
                else {
                    for (let key in param) {
                        if (['image_path', 'case_image_path', 'current_img'].includes(key)) {
                            param[key] = await handleUrlByType(param[key]);
                        }
                        else if ('img_paths' === key) {
                            let newList = [];
                            for (let imagePath of param[key]) {
                                newList.push(await handleUrlByType(imagePath));
                            }
                            param[key] = newList;
                        }
                    }
                }
            }
        }
        return paramsList;
    }

    async downloadCloudRequest() {
        // 获取本机设备号
        let lazyoneId = null;
        if (null === this.lazyId) {
            try {
                this.lazyId = await this.lazyoneId();
            }
            catch(err) {console.log(err) }
            if (null === this.lazyId) {
                return;
            }
        }
        else {
            lazyoneId = this.lazyId;
        }
        // 判断策略是否启动
        if (2 !== this.status) {
            return;
        }
        // 判断 QPS 是否超限
        if (this.requestTimes <= 0) {
            return;
        }

        // 判断是否开启了云端策略模式
        let { cloudServerMode = 1 } = JSON.parse(fs.readFileSync(`${SYS_PROFILE_DIR}/mode.json`));
        if (2 !== cloudServerMode) {
            return;
        }
        // 获取一个任务
        let res = await cloudServerRequest({
            baseURL: global.cloudAddress,
            path: '/core/strategy/server/executor/consume',
            body: {
                deviceId: lazyoneId
            },
            headers: global.superHeader,
            method: 'POST'
        });
        if (510 === res.code) {
            return;
        }
        if (0 !== res.code && 510 !== res.code) {
            this.logger.error(`${getLogTag('handleCloudRequest')} 策略获取云端任务失败 ${res.msg}`);
            return;
        }
        // 执行一个任务
        let taskId = res.data.taskId;
        this.logger.info(`${getLogTag('handleCloudRequest')} 拉取策略任务 taskId ${taskId}`);
        let startTime = currentTimestamp();
        let code = 0;
        let taskResult = null;
        try {
            taskResult = await this.sendToServer(
                res.data.taskName,
                await this.handlePicUrl({ paramsList: JSON.parse(res.data.taskInfo), type: 'down' })
            );
        }
        catch (err) {
            if ('JsonrpcError' !== err.type) {
                code = -32300;
                taskResult = {
                    code: -32300,
                    message: 'localServer 请求失败',
                    data: {
                        method: res.data.taskName,
                        message: 'localServer 请求失败',
                        stack: err.stack
                    }
                }
            }
            else {
                code = err.code;
                taskResult = {
                    code: err.code,
                    message: err.message,
                    data: err.data
                };
            }
        }
        // 处理一个长截图的特殊上传逻辑
        if (undefined !== taskResult?.result?.base64_img) {
            taskResult.result.base64_img = await uploadFromFile(
                await cacheScreenshotForRandomFile(Buffer.from(taskResult.result.base64_img, 'base64')),
                `/lazycloud/${currentTimestamp()}-${uuidv4()}.jpg`,
                { retryCount: 3 },
                { timeout: 180000 }
            );
        }
        // 回调任务结果
        for (let i = 1; i <= 3; i++) {
            let error = null;
            try {
                let res = await cloudServerRequest({
                    baseURL: global.cloudAddress,
                    path: '/core/strategy/server/executor/callback',
                    body: {
                        taskId,
                        code,
                        taskResult: JSON.stringify(taskResult)
                    },
                    headers: global.superHeader,
                    method: 'POST'
                });
                if (0 === res.code) {
                    break;
                }
                throw new Error(`回调云端失败 ${res.msg}`);
            }
            catch (err) {
                error = err;
            }
            if (3 === i) {
                this.logger.error(`${getLogTag('handleCloudRequest')} taskId ${taskId} 策略任务回调云端失败 ${error.stack}`);
            }
        }
        this.logger.info(`${getLogTag('handleCloudRequest')} 完成策略任务 taskId ${taskId} 耗时 ${currentTimestamp() - startTime}ms`);
    }

    async uploadCloudRequest({ taskName, taskInfo = [], timeout = 120000 }) {
        try {
            let res = await cloudServerRequest({
                baseURL: global.cloudAddress,
                path: '/core/strategy/server/task/create',
                body: {
                    taskName,
                    taskInfo: JSON.stringify(await this.handlePicUrl({ paramsList: taskInfo, type: 'up' })),
                    timeout: parseInt(timeout / 1000, 10)
                },
                headers: global.superHeader,
                method: 'POST'
            });
            if (0 !== res.code) {
                throw new Error('创建云端策略任务失败');
            }
            let taskId = res.data.taskId;
            let cloudResult = null;
            let startTime = currentTimestamp();
            while (currentTimestamp() < startTime + timeout) {
                let isEnd = false;
                try {
                    let res = await cloudServerRequest({
                        baseURL: global.cloudAddress,
                        path: '/core/strategy/server/task/query',
                        body: { taskId },
                        headers: global.superHeader,
                        method: 'POST'
                    });
                    if (0 !== res.code) {
                        throw new Error(`获取 ${taskId} 任务结果失败`);
                    }
                    let { status, result } = res.data;
                    result = JSON.parse(result);
                    if (2 === status) {
                        cloudResult = result;
                        isEnd = true;
                        break;
                    }
                    else if (3 === status) {
                        isEnd = true;
                        throw new JsonrpcError(result);
                    }
                    else if (4 === status) {
                        isEnd = true;
                        throw new JsonrpcError({
                            code: -32300,
                            message: '云端策略任务被取消',
                            data: {
                                method: taskName,
                                message: '云端策略任务被取消',
                                stack: null
                            }
                        });
                    }
                }
                catch (err) {
                    this.logger.error(`${getLogTag('handleCloudRequest')} taskId ${taskId} 策略任务获取结果失败 ${err.stack}`);
                    if (isEnd) {
                        throw err;
                    }
                }
                await delay(200);
            }
            return cloudResult;
        }
        catch (err) {
            if ('JsonrpcError' === err.type) {
                throw err;
            }
            this.logger.error(`${getLogTag('handleCloudRequest')} 云端 localServer 请求异常 ${err.stack}`)
            throw new JsonrpcError({
                code: -32300,
                message: '云端 localServer 请求失败',
                data: {
                    method: taskName,
                    message: err.message,
                    stack: err.stack
                }
            });
        }
    }

    async sendToServer(method, params = [], timeout = 120000) {
        let requestId = this.serverMsgId++;
        let startTime = currentTimestamp();
        // 命令行模式下直接上传请求体
        if (!IS_LOCALTOOL) {
            let result = await this.uploadCloudRequest({
                taskName: method,
                taskInfo: params,
                timeout
            });
            this.logger.info(
                `${getLogTag('send')} [id: ${requestId}][method:${method}]` +
                ` 云端耗时 ${currentTimestamp() - startTime}ms`
            );
            return result;
        }
        // 先行判断服务器状态
        if (2 !== this.status && 'status' !== method) {
            let msg = '';
            if (0 === this.status) {
                msg = '算法服务器未启动';
            }
            if (1 === this.status) {
                msg = '算法服务器正在启动中, 请稍后';
            }
            if (3 === this.status) {
                msg = '算法服务器被关闭';
            }
            let err = Object.create(null);
            Error.captureStackTrace(err);
            throw new JsonrpcError({
                code: -32300,
                message: msg,
                data: {
                    method,
                    message: msg,
                    stack: err.stack
                }
            });
        }
        try {

            this.requestTimes--;
            let { result, error } = await axios.post(
                serverUrl,
                {
                    jsonrpc: '2',
                    id: requestId,
                    method,
                    params
                },
                { timeout }
            ).then(res => res.data);

            this.logger.info(
                `${getLogTag('send')} [id: ${requestId}][method:${method}]` +
                ` 耗时 ${currentTimestamp() - startTime}ms`
            );

            if (error) {
                throw new JsonrpcError(error);
            }
            return result;
        }
        catch (err) {
            if ('status' === method) {
                throw err;
            }

            this.logger.error(getLogTag('send') + `[method:${method}] 请求失败 ${err.message} ${err.stack}`);

            if ('JsonrpcError' === err.type) {
                throw err;
            }
            if (err.message.includes('ms exceeded')) {
                throw new JsonrpcError({
                    code: -32300,
                    message: `localServer 请求超时 ${timeout}ms`,
                    data: {
                        method,
                        message: err.message,
                        stack: err.stack
                    }
                });
            }
            throw new JsonrpcError({
                code: -32300,
                message: 'localServer 请求失败',
                data: {
                    method,
                    message: err.message,
                    stack: err.stack
                }
            });
        }
        finally {
            this.requestTimes++;
        }
    }
};

const init = async ({ name, fatherName }, { resourcePath }) => {
    worker = new LocalServerWorker({ name, fatherName }, { resourcePath });
    if (IS_LOCALTOOL) {
        await worker.initPath();
    }
    await worker.ready();
}

const get = () => worker;

module.exports = { init, get };