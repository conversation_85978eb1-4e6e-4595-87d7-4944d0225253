/**
 * @create 王家麒@2023.10.08
 */
const { cloudServerRequest } = require('../../../util/request');

const getLogTag = method => `[kafka.entry.case.${method}]`;

const uploadCaseImage = async (worker, { image, nodeId, stepId, recordId, versionType }) => {
    try {
        console.log(111);
        let screenshot = await worker.callFront(
            'file.uploadImageToBos',
            { imgBase64: image, nodeId, stepId }
        );
        console.log(screenshot);
        let res = await cloudServerRequest({
            path: '2.0' === versionType ? '/lazyone/step/report/screenshot' : '/core/case/node/step/screenshot/report',
            body: {
                id: stepId,
                stepId,
                screenshot
            },
            headers: global.superHeader,
            method: 'POST'
        });
        console.log(res);
        if (0 !== res.code) {
            throw new Error(`截图同步失败 ${res.msg}`);
        }
        await worker.sendToFront(
            'device.asyncRecord',
            {
                slotName: 'screenshotUrl',
                slotInfo: {
                    stepId,
                    recordId,
                    screenshotUrl: screenshot
                }
            }
        );
    }
    catch (err) {
        worker.logger.warn(`${getLogTag('uploadCaseImage')} 图片消费失败 ${err.stack}`);
        // 再重新丢回消息队列，这个消息必须消费成功
        await worker.pushKafka('uploadImage', {
            image,
            nodeId,
            stepId,
            recordId,
            versionType
        });
    }
};


module.exports = { uploadCaseImage };