/**
 * @create 王家麒@2023.02.06
 */
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { time: { currentTimestamp } } = require('@baidu/bat-util');
const { isEmpty } = require('../../util/param');
const { decodeWebBase64, cacheScreenshot, getImageBorder } = require('../../util/image');
const { uploadFromFile } = require('../../util/bos/index');
const { IMAGE_NODE_DIR, CACHE: { CACHE_FILE_DIR } } = require('../../config');

const uploadImageToBos = async (worker, { imgBase64 = '', nodeId = undefined, stepId = undefined }) => {
    isEmpty({ imgBase64, stepId, nodeId });
    console.log(999)
    // 防止该图片已经是一个网络地址
    if (imgBase64.startsWith('http')) {
        return imgBase64;
    }
    // 将 base64 转化会 buffer
    let imgBuf = await decodeWebBase64(imgBase64);
    // 存本地文件, 加入 uuid 尾缀防止快速调用重名
    let fileName = `${nodeId}-${stepId}-${currentTimestamp()}-${uuidv4()}`;
    let imgPath = cacheScreenshot(imgBuf, fileName);
    // 上传 bos
    console.log(888)
    let bosLink = await uploadFromFile(
        imgPath,
        `/lazy-one/${fileName}.jpg`,
        { retryCount: 3 },
        { timeout: 180000 }
    );
    console.log(777)
    // 转存图片信息和尺寸信息到本地
    fs.copyFileSync(imgPath, `${IMAGE_NODE_DIR}/${fileName}.jpg`);
    fs.writeFileSync(
        `${IMAGE_NODE_DIR}/${fileName}.json`,
        JSON.stringify(await getImageBorder(imgBuf))
    );
    console.log(666)
    console.log(bosLink)
    return bosLink;
};

const uploadDomToBos = async (worker, { dom = '', nodeId = undefined, stepId = undefined }) => {
    isEmpty({ dom, stepId, nodeId });
    // 存本地文件, 加入 uuid 尾缀防止快速调用重名
    let fileName = `${nodeId}-${stepId}-${currentTimestamp()}-${uuidv4()}.json`;
    let filePath = `${CACHE_FILE_DIR}/${fileName}`;
    fs.writeFileSync(filePath, dom);
    // 上传 bos
    let bosLink = await uploadFromFile(
        filePath,
        `/lazy-one/${fileName}`,
        { retryCount: 3 },
        { timeout: 180000 }
    );
    return bosLink;
};

const uploadFileToBos = async (worker, { filePath }) => {
    isEmpty({ filePath });
    if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
    }
    let fileName = path.parse(filePath).base;
    // 上传 bos
    return await uploadFromFile(
        filePath,
        `/lazy-one/${currentTimestamp()}-${uuidv4()}/${fileName}`,
        { retryCount: 3 },
        { timeout: 180000 }
    );
};

const getFileCacheData = async (worker, { bosLink }) => {
    isEmpty({ bosLink });
    try {
        let cacheData = await worker.send('data', 'getFileCacheData');
        if (undefined === cacheData[bosLink]) {
            let fileName = bosLink.split('/').pop().split('.')[0];
            let inferFile = `${IMAGE_NODE_DIR}/${fileName}`;
            if (fs.existsSync(`${inferFile}.jpg`) && fs.existsSync(`${inferFile}.json`)) {
                return {
                    isCache: true,
                    link: `file://${inferFile}.jpg`,
                    size: JSON.parse(fs.readFileSync(`${inferFile}.json`))
                };
            }
            return {
                isCache: false,
                link: bosLink,
                size: { height: -1, width: -1 }
            };
        }
        else if (fs.existsSync(cacheData[bosLink].info)) {
            return {
                isCache: true,
                link: cacheData[bosLink].img,
                size: JSON.parse(fs.readFileSync(cacheData[bosLink].info))
            };
        }
        else {
            return {
                isCache: false,
                link: bosLink,
                size: { height: -1, width: -1 }
            };
        }
    }
    catch (err) {
        worker.logger.error(`[front] file.getFileCacheData 获取图片缓存异常 ${err.stack}`)
        return {
            isCache: false,
            link: bosLink,
            size: { height: -1, width: -1 }
        };
    }
};

module.exports = { uploadImageToBos, uploadDomToBos, uploadFileToBos, getFileCacheData };