import { defineConfig } from 'umi';
import path from 'path';
import MonacoWebpackPlugin from 'monaco-editor-webpack-plugin';
import fs from 'fs';
import routes from './routes';

const ENV_MODE = process.env.ENV_MODE; // 测试环境(rmtc):dev; 众测：test; 线上: prod; 开发环境: undefine
const BUILD_ENV = process.env.BUILD_ENV;
const NODE_ENV = process.env.NODE_ENV;

const publicPath =
    'electron' !== BUILD_ENV && NODE_ENV === 'production' ? 'https://fe-cdn.cdn.bcebos.com/project/qamate/' : '';

// 判断是否为线上环境
const isOnlineEnv = process.env.ENV_MODE === 'prod';
const isPreviewEnv = process.env.ENV_MODE === 'test';

// 根据环境变量确定API代理目标
const getProxyTarget = (type) => {
    if (isOnlineEnv) {
        return 'https://qamate.baidu-int.com';
    }
    if (isPreviewEnv) {
        return 'https://pioneer.baidu-int.com';
    }
    // knowledgeManager
    if (type === 'knowledgeManager') {
        return 'http://knowledge-manager.test.megqaep.appspace.baidu.com';
    }
    // autogression
    if (type === 'autogression') {
        return 'http://autogression.sandbox.megqaep.appspace.baidu.com';
    }
    return 'http://rmtc-offline.bcc-bdbl.baidu.com';
};

export default defineConfig({
    title: 'QAMate',
    outputPath: 'electron' === BUILD_ENV ? '../../qamate-native/core/public/front' : 'dist',
    links: [{ rel: 'icon', href: 'https://fe-cdn.bj.bcebos.com/public/qamate/icon.png' }],
    history: {
        type: 'hash'
    },
    publicPath,
    hash: true,
    esbuildMinifyIIFE: true,
    define: {
        BUILD_ENV: BUILD_ENV,
        MEGA_SDK_ENV: ENV_MODE === 'prod' ? 'prod' : 'beta',
        MEGA_SDK_APPID: 'yGUx1ItoPqMl'
    },
    chainWebpack(memo) {
        memo.plugin('monaco-editor').use(new MonacoWebpackPlugin(), [
            {
                languages: ['json', 'javascript', 'typescript', 'html', 'css'],
                filename: 'static/[name].worker.js' // Worker 文件打包到 static 目录
            }
        ]);
    },
    routes,
    plugins: [require.resolve('@umijs/plugins/dist/antd')],
    mock: {},
    alias: {
        HOC: path.join(__dirname, 'src/hoc'),
        FEATURES: path.join(__dirname, 'src/features'),
        COMMON: path.join(__dirname, 'src/common'),
        RESOURCES: path.join(__dirname, 'src/resources'),
        HOOKS: path.join(__dirname, 'src/hooks'),
        PAGES: path.join(__dirname, 'src/pages'),
        LAYOUTS: path.join(__dirname, 'src/layouts'),
        PACKAGES: path.join(__dirname, 'src/packages'),
        '@step': path.resolve(__dirname, 'src/common/components/TreeComponents/Step')
    },
    antd: {},
    mfsu: {},
    proxy: {
        '/lazymind': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazymind': '/lazymind' }
        },
        '/regression': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/regression': '/regression' }
        },
        '/tree': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/tree': '/tree' }
        },
        '/lazyone': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazyone': '/lazyone' }
        },
        '/common': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/common': '/common' }
        },
        '/lazycloud': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazycloud': '/lazycloud' }
        },
        '/base': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/base': '/base' }
        },
        '/lazydevice': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazydevice': '/lazydevice' }
        },
        '/icafe': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/icafe': '/icafe' }
        },
        '/core': {
            'target': 'http://rmtc-offline.bcc-bdbl.baidu.com:8119',
            // target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/core': '/core' }
        },
        '/iteration': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/iteration': '/iteration' }
        },
        '/lazyailab': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/lazyailab': '/lazyailab' }
        },
        '/knowledgeManager': {
            target: 'http://knowledge-manager.test.megqaep.appspace.baidu.com',
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/knowledgeManager': '/knowledgeManager' }
        },
        '/itp': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/itp': '/itp' }
        },
        '/autogression': {
            target: 'http://autogression.sandbox.megqaep.appspace.baidu.com',
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/autogression': '/autogression' }
        },
        '/auth': {
            target: getProxyTarget(),
            changeOrigin: true,
            onProxyReq:
                isOnlineEnv || isPreviewEnv
                    ? (proxyReq) => {
                          // 线上环境需要添加cookie
                          proxyReq.setHeader('Cookie', fs.readFileSync('./.cookie_' + ENV_MODE + '.txt'));
                      }
                    : undefined,
            pathRewrite: { '^/auth': '/auth' }
        }
    }
});
