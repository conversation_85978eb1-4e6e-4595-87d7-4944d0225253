import { post, get } from 'COMMON/utils/requestUtils';
import { withTreeMapUpdate } from 'COMMON/utils/treeUtils';
// 获取步骤列表
export const getStepList = (params) => {
    return post('/core/case/node/step/list', params);
};

// 获取用例路径步骤详情
export const getStepListWithPath = (params) => {
    return post('/core/case/path', params);
};

// 创建步骤
export const createStep = withTreeMapUpdate((params) => {
    return post('/core/case/node/step/create', params).then((result) => {
        return result;
    });
}, 'update');

// 创建步骤组的步骤
export const createStepInGroup = (params) => {
    return post('/core/case/node/step/group/create', params);
};

// 编辑步骤
export const updateStep = (params) => {
    // 如果是步骤组，则将步骤组参数中的步骤清空
    if (['stepGroup', 'runTemplate']?.includes(params?.stepInfo?.params?.type)) {
        params.stepInfo.params.params.step = [];
    }
    // 智能定位，强制传图片
    if (params?.stepInfo?.type === 11) {
        params.forceTrust = true;
    }
    return post('/core/case/node/step/update', params);
};

// 步骤删除态更新
export const updateStepAttrInIsDel = (params) => {
    return post('/core/case/node/step/updateDel', params);
};

// 删除步骤
export const deleteStep = (params) => {
    return post('/core/case/node/step/delete', params);
};

// 步骤拷贝
export const copyStepList = (params) => {
    return post('/core/case/node/step/copy', params);
};

// 批量移动步骤
export const batchMoveStepList = (params) => {
    return post('/core/case/node/step/move', params);
};

// 批量复制步骤
export const batchCopyStepList = (params) => {
    return post('/core/case/node/step/batch/copy', params);
};

// 创建节点钩子
export const createNodeHookStep = (params) => {
    return post('/core/case/node/hook/create', params);
};
// 删除setup、teardown节点
export const deleteSetupTeardownNode = (params) => {
    return post('/core/case/node/hook/delete', params);
};
