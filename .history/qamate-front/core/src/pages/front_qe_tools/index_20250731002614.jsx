import { useEffect } from 'react';
import { Outlet } from 'umi';
import moment from 'moment';
import { isEmpty } from 'lodash';
import 'moment/locale/zh-cn';
import { message } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import newBaseModel from 'COMMON/models/newBaseModel';
import commonModel from 'COMMON/models/commonModel';
import { getQueryParams } from 'COMMON/utils/utils';
import electron from 'COMMON/utils/electron';

moment.locale('zh-cn');

function IndexPage(props) {
    const {
        currentSpace,
        setIpPort,
        setOcrCharset,
        deviceList,
        setDeviceList,
        currentDevice,
        osType,
        curOsType,
        setCurrentDevice,
        getTagList,
        getDefaultConfig,
        getCreationConfig,
        getBugConfig,
        getDocPromptList,
        getPromptList,
        statusCode
    } = props;
    const query = getQueryParams();

    // ocr 表达式获取
    useEffect(() => {
        async function func() {
            let _str = await electron.send('device.record.ocrCharset');
            setOcrCharset(_str.pattern || '');
        }
        if (1 === statusCode && isElectron()) {
            func();
        }
    }, [statusCode]);

    // 设备状态全局监听
    useEffect(() => {
        const handler = ({ deviceType, deviceId, nickModal }) => {
            console.log('device.connect:', deviceType, deviceId, nickModal);
            message.success(`${deviceType} 设备 ${deviceId} 连接成功`);
        };
        if (isElectron()) {
            electron.on('device.connect', handler);
            return () => electron.remove('device.connect', handler);
        }
    }, []);

    useEffect(() => {
        const handler = ({ deviceType, deviceId, nickModal }) => {
            message.warning(`${deviceType} 设备 ${deviceId} 断开连接`);
            if (currentDevice?.deviceId === deviceId) {
                setIpPort({
                    ip: '获取失败',
                    port: null,
                    cert: null
                });
            }
        };
        if (isElectron()) {
            electron.on('device.disconnect', handler);
            return () => electron.remove('device.disconnect', handler);
        }
    }, [currentDevice?.deviceId]);

    useEffect(() => {
        console.log('currentDevice:', currentDevice, osType);
        const handler = ({ android, iOS }) => {
            setDeviceList({
                iOS: iOS,
                android: android
            });
            if (null === currentDevice) {
                if (2 === +osType) {
                    let curDevice = !isEmpty(iOS) ? iOS[0] : null;
                    for (let item of iOS) {
                        if (item.status !== 5) {
                            curDevice = item;
                            break;
                        }
                    }
                    if (currentDevice == null || currentDevice?.deviceId === curDevice?.deviceId) {
                        setCurrentDevice(curDevice);
                    }
                } else {
                    let curDevice = !isEmpty(android) ? android[0] : null;
                    for (let item of android) {
                        if (item.status !== 5) {
                            curDevice = item;
                            break;
                        }
                    }
                    if (currentDevice == null || currentDevice?.deviceId === curDevice?.deviceId) {
                        setCurrentDevice(curDevice);
                    }
                }
            }
        };
        if (isElectron()) {
            electron.on('device.maps', handler);
            return () => electron.remove('device.maps', handler);
        }
    }, [currentDevice?.deviceId, osType]);

    useEffect(() => {
        const handler = ({ deviceType, deviceId, deviceStatus, statusStage }) => {
            for (let device of deviceList[deviceType]) {
                if (device.deviceId === deviceId) {
                    device.status = deviceStatus;
                    device.statusStage = statusStage;
                }
                if (currentDevice?.deviceId === device.deviceId && device.deviceId === deviceId) {
                    getIpPort(device);
                    setCurrentDevice(device);
                }
            }
            setDeviceList(deviceList);
        };
        if (isElectron()) {
            electron.on('device.update', handler);
            return () => electron.remove('device.update', handler);
        }
    }, [deviceList, currentDevice?.deviceId]);

    useEffect(() => {
        if (currentDevice?.deviceId) {
            getIpPort(currentDevice);
        }
    }, [currentDevice]);

    const getIpPort = (device) => {
        if (!device?.status || -1 === [2, 3].indexOf(device?.status)) {
            setIpPort({
                ip: '获取失败',
                port: null,
                cert: null
            });
            return;
        }
        if (2 <= device?.statusStage?.proxy?.status) {
            electron
                .send('proxy.init', {
                    deviceType: device?.deviceType,
                    deviceId: device?.deviceId
                })
                .then((res) => {
                    setIpPort(res);
                })
                .catch(() => {});
        } else {
            setIpPort({
                ip: '获取失败',
                port: null,
                cert: null
            });
        }
    };

    useEffect(() => {
        const func1 = async () => {
            try {
                const promises = [
                    getTagList({ productId: Number(currentSpace?.id) }),
                    getDefaultConfig({ productId: Number(currentSpace?.id) }),
                    getCreationConfig({ productId: Number(currentSpace?.id) })
                ];
                Promise.all(promises.map((promise) => promise.catch((error) => ({ error }))));
            } catch (err) {}
        };
        func1();
    }, [currentSpace]);

    // 获取设备列表
    useEffect(() => {
        if (isElectron()) {
            let _osType = osType ?? query?.osType ?? query?.os ?? +curOsType;
            electron
                .send('device.list', { deviceType: 2 === _osType ? 'iOS' : 'android' })
                .then((res) => {
                    let newDeviceList = { ...deviceList };
                    newDeviceList[2 === _osType ? 'iOS' : 'android'] = res;
                    setDeviceList(newDeviceList);
                    let curDevice = !isEmpty(res) ? res[0] : null;
                    for (let item of res) {
                        if (item.status !== 5) {
                            curDevice = item;
                            break;
                        }
                    }
                    if (currentDevice === null) {
                        setCurrentDevice(curDevice);
                    }
                });
        }
    }, [osType, query?.osType, curOsType]);

    // 获取prompt列表
    useEffect(() => {
        const moduleId = currentSpace?.id;
        if (!currentSpace?.id) {
            return;
        }
        const fetchAll = async () => {
            try {
                await Promise.all([
                    getBugConfig({ moduleId }),
                    getPromptList({ moduleId, modelType: 1, moduleName: currentSpace?.name }),
                    getDocPromptList({ moduleId, modelType: 2, moduleName: currentSpace?.name })
                ]);
            } catch (error) {}
        };
        fetchAll();
    }, [currentSpace?.id]);

    return <Outlet />;
}

export default connectModel([baseModel, newBaseModel, commonModel], (state) => ({
    statusCode: state.common.new_base.statusCode,
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    ipPort: state.common.base.ipPort,
    tagList: state.common.base.tagList,
    currentDevice: state.common.base.currentDevice,
    curOsType: state.common.case.curOsType,
    ocrCharset: state.common.base.ocrCharset,
    bugConfig: state.common.base.bugConfig,
    promptList: state.common.base.promptList,
    docPromptList: state.common.base.docPromptList
}))(IndexPage);
