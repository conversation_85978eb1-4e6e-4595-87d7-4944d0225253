import { useEffect, useState } from 'react';
import classnames from 'classnames';
import { useLocation, useNavigate } from 'umi';
import { Layout, Spin } from 'antd';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { getQueryParams } from 'COMMON/utils/utils';
import commonModel from 'COMMON/models/commonModel';
import { NoContent, CustomResizableBox } from 'COMMON/components/common';
import LayoutHeader from 'LAYOUTS/BasicLayout/LayoutHeader';
import {
    getRequestDataList,
    getRequestDataDetail,
    getRequestDataRelationList
} from 'COMMON/api/front_qe_tools/config/request-data';
import ProxyDetail from 'FEATURES/setting/CommonSetting/RequestManage/ProxyDetail';
import LayoutSider from 'FEATURES/setting/CommonSetting/RequestManage/LayoutSider';
import commonStyles from 'PAGES/common.module.less';
import styles from './ProxyManagePage.module.less';

function ProxyManagePage(props) {
    const {
        asider,
        setAsider,
        currentSpace,
        setRequestDataRelationList,
        setRequestDataList,
        currentRequestData,
        setCurrentRequestData
    } = props;
    const [loading, setLoading] = useState(true);
    const [detailLoading, setDetailLoading] = useState(false);
    const location = useLocation();
    const navigate = useNavigate();
    const query = getQueryParams();

    useEffect(() => {
        EventBus.on('refreshRequestDataList', refreshRequestDataList);
        return () => {
            EventBus.off('refreshRequestDataList', refreshRequestDataList);
        };
    }, [currentSpace?.id]);

    useEffect(() => {
        if (currentSpace?.id) {
            refreshRequestDataList();
            setRequestDataRelationList({ status: 1, data: {} });
            getRequestDataRelationList({ moduleId: currentSpace?.id }).then((res) => {
                setRequestDataRelationList({
                    status: 2,
                    data: res ?? {}
                });
            });
        }
    }, [currentSpace?.id]);

    useEffect(() => {
        if (query?.digitalNodeId) {
            getDigitalNodeDetail({ digitalNodeId: query.digitalNodeId });
        } else {
            setCurrentRequestData(null);
        }
    }, [query?.digitalNodeId]);

    const refreshRequestDataList = async () => {
        try {
            setLoading(true);
            let res = await getRequestDataList({ moduleId: currentSpace?.id });
            setRequestDataList(res?.tree);
            setLoading(false);
        } catch (err) {
            setLoading(false);
        }
    };

    // 获取数据详情
    const getDigitalNodeDetail = async (node) => {
        try {
            setDetailLoading(true);
            let res = await getRequestDataDetail({
                digitalNodeId: node?.digitalNodeId
            });
            setCurrentRequestData({
                nodeName: res?.nodeName ?? '未知',
                digitalNodeId: +node?.digitalNodeId,
                nodeType: res?.nodeType ?? 2,
                digitalInfo:
                    typeof res?.digitalInfo === 'string'
                        ? JSON.parse(res?.digitalInfo || '{}')
                        : res?.digitalInfo
            });
            setDetailLoading(false);
        } catch (err) {
            setDetailLoading(false);
        }
    };

    const onClick = async (node) => {
        let newQuery = {
            moduleId: currentSpace?.id
        };
        if (node?.digitalNodeId) {
            newQuery.digitalNodeId = +node?.digitalNodeId;
        }
        navigate(
            stringifyUrl({
                url: location.pathname,
                query: newQuery
            })
        );
    };

    const onRefresh = (data) => {
        refreshRequestDataList();
        onClick(data);
    };

    return (
        <Layout className={styles.layout}>
            <LayoutHeader
                showRunSetting={true}
                onChange={() => {
                    let query = {
                        moduleId: query?.moduleId ?? query?.productId
                    };
                    navigate(
                        stringifyUrl({
                            url: location.pathname,
                            query
                        })
                    );
                }}
            />
            <Layout hasSider>
                <CustomResizableBox asider={asider} setAsider={setAsider} border={false}>
                    <div style={{ width: '100%' }}>
                        <LayoutSider
                            className={styles.layoutSider}
                            loading={loading}
                            setLoading={setLoading}
                            onClick={onClick}
                            currentRequestData={currentRequestData}
                            onRefresh={onRefresh}
                        />
                    </div>
                </CustomResizableBox>
                <div
                    className={classnames(commonStyles.rightLayout, {
                        [commonStyles.intergrationRightLayout]: asider
                    })}
                >
                    <Layout className={styles.right}>
                        <Spin spinning={detailLoading} tip="加载中...">
                            {currentRequestData?.nodeType === 2 ? (
                                <ProxyDetail onRefresh={onRefresh} {...props} />
                            ) : (
                                <NoContent text="暂无信息" className={styles.noContent} />
                            )}
                        </Spin>
                    </Layout>
                </div>
            </Layout>
        </Layout>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    asider: state.common.base.asider,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    wholePath: state.common.base.wholePath,
    requestDataList: state.common.case.requestDataList,
    currentRequestData: state.common.case.currentRequestData,
    requestDataRelationList: state.common.case.requestDataRelationList
}))(ProxyManagePage);
