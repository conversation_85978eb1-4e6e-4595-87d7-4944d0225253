import { useState, useEffect, useCallback } from 'react';
import { Space, Table, Tag, Button, Input, Popover, message, Spin } from 'antd';
import {
    RobotOutlined,
    UserOutlined,
    CloseCircleOutlined,
    BlockOutlined,
    CarryOutOutlined,
    DeleteRowOutlined,
    LoadingOutlined
} from '@ant-design/icons';
import { getSpeedRoundList, updateSpeedRound } from 'COMMON/api/front_qe_tools/lazyperf';

const { TextArea } = Input;

const CalibrateTable = ({
    planId,
    caseNodeId,
    sceneId,
    recordList: propRecordList,
    onCalibrate,
    checkMode = false
}) => {
    const [loading, setLoading] = useState(false);
    const [recordList, setRecordList] = useState(propRecordList || []);
    const [invalidComment, setInvalidComment] = useState('');

    // 获取数据列表
    const fetchRecordList = useCallback(async () => {
        if (!planId || !caseNodeId || !sceneId) {
            return;
        }

        setLoading(true);
        try {
            const response = await getSpeedRoundList({
                planId,
                caseNodeId,
                // sceneId
            });
                setRecordList(response.recordList || []);
        } finally {
            setLoading(false);
        }
    }, [planId, caseNodeId, sceneId]);

    // 初始化数据
    useEffect(() => {
        if (!propRecordList && planId && caseNodeId && sceneId) {
            fetchRecordList();
        } else if (propRecordList) {
            setRecordList(propRecordList);
        }
    }, [fetchRecordList, propRecordList, planId, caseNodeId, sceneId]);

    // 计算智能TTI
    const calculateAutoTTI = (record) => {
        const { correctDetail = {} } = record;
        const { auto = {} } = correctDetail;

        if (!auto.firstFrameTimestamp || !auto.lastFrameTimestamp) {
            return '--';
        }

        const tti = auto.lastFrameTimestamp - auto.firstFrameTimestamp;
        if (tti < 0) {
            return <span style={{ color: '#ff0000' }}>首帧大于尾帧, 异常</span>;
        }

        return `${tti} ms`;
    };

    // 计算人工TTI
    const calculateManualTTI = (record) => {
        const { correctDetail = {} } = record;
        const { auto = {}, manual = {} } = correctDetail;

        // 计算首尾帧时间戳 - 如果人工未校准则使用智能校准的结果
        const firstTimestamp = manual.firstFrameStatus === 1
            ? manual.firstFrameTimestamp
            : auto.firstFrameTimestamp;
        const lastTimestamp = manual.lastFrameStatus === 1
            ? manual.lastFrameTimestamp
            : auto.lastFrameTimestamp;

        if (!firstTimestamp || !lastTimestamp) {
            return '--';
        }

        const tti = lastTimestamp - firstTimestamp;
        if (tti < 0) {
            return <span style={{ color: '#ff0000' }}>首帧大于尾帧, 异常</span>;
        }

        return `${tti} ms`;
    };

    // 获取记录状态
    const getRecordStatus = (record) => {
        const { correctDetail = {}, isValid } = record;
        const { auto = {}, manual = {} } = correctDetail;

        if (isValid === 1) {
            return {
                status: '已废弃',
                icon: <CloseCircleOutlined />,
                color: 'error'
            };
        }

        // 检查是否正在校准中（智能校准未完成）
        if (auto.firstFrameStatus === 0 || auto.lastFrameStatus === 0) {
            return {
                status: '校准中',
                icon: <LoadingOutlined />,
                color: 'warning'
            };
        }

        // 检查人工校准状态
        const manualStatus = Math.max(manual.firstFrameStatus || 0, manual.lastFrameStatus || 0);
        if (manualStatus === 1) {
            return {
                status: '人工校准',
                icon: <UserOutlined />,
                color: 'success'
            };
        }

        // 智能校准完成但未人工校准
        return {
            status: '智能校准',
            icon: <RobotOutlined />,
            color: 'processing'
        };
    };

    // 处理记录废弃
    const handleInvalidRecord = async (recordSceneId, comment = '') => {
        try {
            const response = await updateSpeedRound({
                recordSceneId,
                isValid: 1,
                validComment: comment
            });

            if (response.code === 0) {
                // 更新本地状态
                const newRecordList = recordList.map(record =>
                    record.recordSceneId === recordSceneId
                        ? { ...record, isValid: 1, invalidComment: comment }
                        : record
                );
                setRecordList(newRecordList);
                message.success('记录已废弃');
            } else {
                message.error(response.msg || '废弃失败');
            }
        } catch (error) {
            console.error('废弃记录失败:', error);
            message.error('废弃记录失败');
        }
    };

    // 处理记录恢复
    const handleRestoreRecord = async (recordSceneId) => {
        try {
            const response = await updateSpeedRound({
                recordSceneId,
                isValid: 0,
                validComment: ''
            });

            if (response.code === 0) {
                // 更新本地状态
                const newRecordList = recordList.map(record =>
                    record.recordSceneId === recordSceneId
                        ? { ...record, isValid: 0, invalidComment: '' }
                        : record
                );
                setRecordList(newRecordList);
                message.success('记录已恢复');
            } else {
                message.error(response.msg || '恢复失败');
            }
        } catch (error) {
            console.error('恢复记录失败:', error);
            message.error('恢复记录失败');
        }
    };

    // 处理校准操作
    const handleCalibrate = (record) => {
        if (onCalibrate) {
            onCalibrate(record);
        }
    };

    // 表格列定义
    const columns = [
        {
            title: 'ID',
            width: 100,
            dataIndex: 'recordSceneId',
            key: 'recordSceneId'
        },
        {
            title: '智能TTI',
            width: 200,
            key: 'smartTTI',
            render: (record) => {
                if (record.isValid === 1) {
                    return <span>--</span>;
                }
                return calculateAutoTTI(record);
            }
        },
        {
            title: '人工TTI',
            width: 200,
            key: 'manualTTI',
            render: (record) => {
                if (record.isValid === 1) {
                    return <span>--</span>;
                }
                return calculateManualTTI(record);
            }
        },
        {
            title: '状态',
            key: 'status',
            render: (record) => {
                const statusInfo = getRecordStatus(record);
                return (
                    <Tag icon={statusInfo.icon} color={statusInfo.color}>
                        {statusInfo.status}
                    </Tag>
                );
            }
        },
        {
            title: '操作',
            width: 300,
            key: 'operate',
            render: (record) => {
                if (record.isValid === 1) {
                    // 已废弃的记录显示恢复按钮
                    return (
                        <Space size="middle">
                            <Button
                                shape="round"
                                size="small"
                                icon={<CarryOutOutlined />}
                                onClick={() => handleRestoreRecord(record.recordSceneId)}
                            >
                                恢复
                            </Button>
                            {record.invalidComment && (
                                <span style={{ color: '#999' }}>
                                    因 {record.invalidComment} 被废弃
                                </span>
                            )}
                        </Space>
                    );
                }

                // 正常记录显示校准和废弃按钮
                return (
                    <Space size="middle">
                        <Button
                            shape="round"
                            size="small"
                            icon={<BlockOutlined />}
                            onClick={() => handleCalibrate(record)}
                        >
                            校准
                        </Button>
                        <Popover
                            title="废弃理由"
                            trigger="click"
                            content={
                                <div>
                                    <TextArea
                                        value={invalidComment}
                                        onChange={(e) => setInvalidComment(e.target.value)}
                                        placeholder="废弃备注, 可为空"
                                        autoSize={{ minRows: 3, maxRows: 5 }}
                                    />
                                    <Button
                                        danger
                                        style={{ marginTop: 5 }}
                                        onClick={() => {
                                            handleInvalidRecord(record.recordSceneId, invalidComment);
                                            setInvalidComment('');
                                        }}
                                    >
                                        确认
                                    </Button>
                                </div>
                            }
                        >
                            <Button
                                danger
                                shape="round"
                                size="small"
                                icon={<DeleteRowOutlined />}
                            >
                                废弃
                            </Button>
                        </Popover>
                    </Space>
                );
            }
        }
    ];

    // 如果启用检查模式，添加检查列
    if (checkMode) {
        columns.splice(columns.length - 1, 0, {
            title: '检查',
            key: 'check',
            width: 100,
            render: () => (
                <Button
                    shape="round"
                    size="small"
                    icon={<BlockOutlined />}
                >
                    检查
                </Button>
            )
        });
    }

    if (loading) {
        return <Spin size="large" />;
    }

    return (
        <Table
            style={{ width: '100%' }}
            size="small"
            columns={columns}
            dataSource={recordList}
            rowKey={record => record.recordSceneId}
            pagination={{
                showSizeChanger: false,
                hideOnSinglePage: true,
                showQuickJumper: true,
                pageSize: 10,
                showTotal: (total) => `共${total}条`
            }}
            bordered
        />
    );
};

export default CalibrateTable;
