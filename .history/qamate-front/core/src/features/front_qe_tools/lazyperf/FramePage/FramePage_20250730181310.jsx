import { useEffect, useState } from 'react';
import { getSpeedRoundList } from 'COMMON/api/front_qe_tools/lazyperf';
import CalibrateTable from './components/CalibrateTable';
import Operate from '../components/Operate';
import styles from './FramePage.module.less';

const FramePage = (props) => {
    const { curTask } = props;
    const [curCaseNodeId, setCurCaseNodeId] = useState(null);
    const [curSceneId, setCurSceneId] = useState(null);
    const [sceneList, setSceneList] = useState([]);
    const [recordList, setRecordList] = useState([]);

    // 初始化默认选中第一个用例
    useEffect(() => {
        if (curTask?.planParams?.caseNodeParams?.caseNodeList?.length > 0 && !curCaseNodeId) {
            const firstCaseNode = curTask.planParams.caseNodeParams.caseNodeList[0];
            console.log('FramePage: 初始化设置默认 caseNodeId:', firstCaseNode.caseNodeId);
            setCurCaseNodeId(firstCaseNode.caseNodeId);
        }
    }, [curTask]);


    const caseNodeOptions = (curTask?.planParams?.caseNodeParams?.caseNodeList || []).map(
        (item) => ({
            value: item?.caseNodeId,
            key: item?.caseNodeId,
            label: item?.caseNodeName
        })
    );

    const sceneListOptions = (sceneList || []).map((item) => ({
        value: item?.id,
        key: item?.id,
        label: item?.name
    }));

    useEffect(() => {
        const curCaseNode = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
            (item) => item?.caseNodeId === curCaseNodeId
        );
        const newSceneList = curCaseNode?.sceneList || [];
        setSceneList(newSceneList);

        // 自动选中第一个场景
        if (newSceneList.length > 0 && !curSceneId) {
            console.log('FramePage: 设置默认 sceneId:', newSceneList[0].id);
            setCurSceneId(newSceneList[0].id);
        }
    }, [curCaseNodeId, curSceneId]);

    useEffect(() => {
        async function func() {
            if (!curTask) {
                console.log('FramePage: curTask 为空，跳过数据获取');
                return;
            }

            if (!curTask.planId) {
                console.log('FramePage: planId 为空，跳过数据获取');
                return;
            }

            if (!curCaseNodeId) {
                console.log('FramePage: curCaseNodeId 为空，跳过数据获取');
                return;
            }

            const params = {
                planId: curTask.planId,
                caseNodeId: curCaseNodeId,
                sceneId: curSceneId // sceneId 可以为空，某些接口可能不需要
            };

            console.log('FramePage: 调用 getSpeedRoundList，参数:', params);
            console.log('FramePage: curTask 详情:', curTask);

            try {
                let res = await getSpeedRoundList(params);

                // 处理不同的响应格式
                let recordList = [];
                if (res) {
                    if (res.data && res.data.recordList) {
                        // 格式1: { code: 0, data: { recordList: [...] } }
                        recordList = res.data.recordList;
                    } else if (res.recordList) {
                        // 格式2: { recordList: [...] }
                        recordList = res.recordList;
                    } else if (Array.isArray(res)) {
                        // 格式3: 直接返回数组
                        recordList = res;
                    }
                }

                console.log('FramePage: 处理后的 recordList:', recordList);
                setRecordList(recordList);
            } catch (error) {
                console.error('FramePage: getSpeedRoundList 调用失败:', error);
                setRecordList([]);
            }
        }
        func();
    }, [curTask, curSceneId, curCaseNodeId]);

    return (
        <>
            <div className={styles.taskPage}>
                <Operate
                    planId={curTask?.planId}
                    caseNodeOptions={caseNodeOptions}
                    setCurCaseNodeId={setCurCaseNodeId}
                    setCurSceneId={setCurSceneId}
                    sceneListOptions={sceneListOptions}
                    recordList={recordList}
                />
                {/* <CalibrateTable recordList={recordList} /> */}
            </div>
        </>
    );
};

export default FramePage;
