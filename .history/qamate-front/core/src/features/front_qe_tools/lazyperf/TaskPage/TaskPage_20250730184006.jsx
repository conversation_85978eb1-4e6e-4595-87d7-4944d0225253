import React, { useState } from 'react';
import { Card, Row, Col, Progress, Tag, Space, Typography, Table, Spin, Button, Popover, message } from 'antd';
import { StopOutlined } from '@ant-design/icons';
import { getFormatTime } from 'COMMON/utils/dateUtils';
import AppendPerfPlanModal from 'COMMON/components/NewAddDropdown/AppendPerfPlanModal';
import { cancelPerfPlan } from 'COMMON/api/front_qe_tools/lazyperf';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import styles from './TaskPage.module.less';

const { Text } = Typography;

const TaskPage = (props) => {
    const { curTask, deviceList } = props;
    const [cancelLoading, setCancelLoading] = useState(false);

    const planParams = curTask?.planParams || {};
    const cloudParmas = curTask?.planParams?.cloudParmas || {};
    const roundDetail = curTask?.roundDetail || {};

    // 取消性能评测
    const handleCancelPerfPlan = async () => {
        if (!curTask?.planId) {
            message.error('任务ID不存在');
            return;
        }

        try {
            setCancelLoading(true);
            await cancelPerfPlan({
                planId: curTask.planId
            });
            message.success('性能评测已取消');
            // 刷新任务列表
            EventBus.emit('refreshPerfPlanList');
        } catch (error) {
            console.error('取消性能评测失败:', error);
            message.error(error?.message || '取消性能评测失败');
        } finally {
            setCancelLoading(false);
        }
    };

    // Popover 内容
    const cancelContent = (
        <div style={{ width: '280px' }}>
            <div style={{ marginBottom: '16px' }}>
                <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>确认取消性能评测</div>
                <div style={{ color: '#666', fontSize: '14px' }}>
                    确定要取消任务 <strong>{curTask?.planId}</strong> 的性能评测吗？
                </div>
                <div style={{ color: '#ff4d4f', fontSize: '12px', marginTop: '4px' }}>
                    ⚠️ 取消后将无法恢复
                </div>
            </div>
            <div style={{ textAlign: 'right' }}>
                <Space>
                    <Button size="small">取消</Button>
                    <Button
                        type="primary"
                        danger
                        size="small"
                        loading={cancelLoading}
                        onClick={handleCancelPerfPlan}
                    >
                        确认取消
                    </Button>
                </Space>
            </div>
        </div>
    );

    // 显示加载状态
    if (!curTask) {
        return (
            <div className={styles.taskPage}>
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '400px'
                    }}
                >
                    <Spin size="large" tip="加载任务信息中..." />
                </div>
            </div>
        );
    }



    // 获取状态标签
    const getStatusTag = (status) => {
        const statusMap = {
            0: { text: '待拆分', color: 'default' },
            1: { text: '待执行', color: 'blue' },
            2: { text: '执行中', color: 'processing' },
            3: { text: '执行完成', color: 'success' }
        };
        const { text, color } = statusMap[status] || { text: '未知状态', color: 'default' };
        return <Tag color={color}>{text}</Tag>;
    };


    return (
        <>
            <div className={styles.taskPage}>
                <div className={styles.operateHeader}>
                    <div>
                        <AppendPerfPlanModal />
                        <Button
                            type="primary"
                            danger
                            shape="round"
                            icon={<StopOutlined />}
                            loading={cancelLoading}
                            onClick={handleCancelPerfPlan}
                            disabled={!curTask?.planId || curTask?.status === 3} // 已完成的任务不能取消
                            style={{ marginLeft: '8px' }}
                        >
                            取消评测
                        </Button>
                    </div>
                </div>

                <div className={styles.detailContainer}>
                    <Card
                        title="任务信息"
                        className={styles.taskInfoCard}
                        extra={getStatusTag(curTask.status)}
                    >
                        <Row gutter={[16, 16]}>
                            <Col span={8}>
                                <Space align="start">
                                    <Text strong>ID:</Text>
                                    <Text>{curTask.planId || '-'}</Text>
                                </Space>
                            </Col>
                            <Col span={8}>
                                <Space align="start">
                                    <Text strong>创建时间:</Text>
                                    <Text>
                                        {curTask.createTime ? getFormatTime(curTask.createTime) : '-'}
                                    </Text>
                                </Space>
                            </Col>
                            <Col span={8}>
                                <Space align="start">
                                    <Text strong>设备ID:</Text>
                                    <Text>{curTask?.planParams?.cloudParams.deviceId || '-'}</Text>
                                </Space>
                            </Col>
                            <Col span={16}>
                                <Space align="start">
                                    <Text strong>执行状态:</Text>
                                    <div>
                                        <Tag color="blue" style={{ marginRight: 8 }}>
                                            期望 {roundDetail?.totalNum || 0} 次
                                        </Tag>
                                        {/* 参考旧逻辑：只有在执行中或执行完成时才显示执行次数 */}
                                        {(curTask.status === 2 || curTask.status === 3) && (
                                            <>
                                                {/* 成功次数：只有当成功次数大于0时才显示 */}
                                                {(roundDetail?.successNum || 0) > 0 && (
                                                    <Tag color="success" style={{ marginRight: 8 }}>
                                                        成功 {roundDetail?.successNum} 次
                                                    </Tag>
                                                )}
                                                {/* 失败次数：只有当失败次数大于0时才显示 */}
                                                {(roundDetail?.failNum || 0) > 0 && (
                                                    <Tag color="error">
                                                        失败 {roundDetail?.failNum} 次
                                                    </Tag>
                                                )}
                                            </>
                                        )}
                                    </div>
                                </Space>
                            </Col>
                        </Row>
                        <Table
                            dataSource={(() => {
                                const caseNodeList = planParams?.caseNodeParams?.caseNodeList || [];
                                const tableData = [];

                                caseNodeList.forEach((caseNode) => {
                                    console.log('caseNode', caseNode);
                                    const sceneList = caseNode.sceneList || [];
                                    sceneList.forEach((scene, sceneIndex) => {
                                        tableData.push({
                                            key: `${caseNode.caseNodeId}-${scene.id}`,
                                            caseName: `用例节点${caseNode.caseNodeId}`,
                                            tti: `${scene.name}: ${1000 + sceneIndex * 200} ms`,
                                            calibration: {
                                                taskStatus: curTask.status,
                                                manualCount: scene.manualCount || 0,
                                                wholeCount: scene.wholeCount || 10,
                                                actualTimes: roundDetail?.totalNum - (roundDetail?.pendingNum || 0), // 总数 - 待执行数 = 已执行数
                                                exceptionTimes: roundDetail?.failNum || 0
                                            }
                                        });
                                    });
                                });

                                return tableData;
                            })()}
                            columns={[
                                {
                                    title: '用例名称',
                                    dataIndex: 'caseName',
                                    key: 'caseName',
                                    align: 'left'
                                },
                                {
                                    title: 'TTI',
                                    dataIndex: 'tti',
                                    key: 'tti',
                                    align: 'left'
                                },
                                {
                                    title: '校准进度',
                                    dataIndex: 'calibration',
                                    key: 'calibration',
                                    align: 'left',
                                    render: (calibrationData) => {
                                        // 参考旧逻辑：如果是待执行状态或者没有成功执行次数，显示 --
                                        if (!calibrationData ||
                                            calibrationData.taskStatus === 0 || // 待拆分
                                            calibrationData.taskStatus === 1 || // 待执行
                                            calibrationData.actualTimes === 0 ||
                                            calibrationData.actualTimes - calibrationData.exceptionTimes === 0) {
                                            return <span>--</span>;
                                        }

                                        // 计算校准进度百分比
                                        const percent = Math.ceil((calibrationData.manualCount / calibrationData.wholeCount) * 100);
                                        return <Progress percent={percent} status={'success'} />;
                                    }
                                }
                            ]}
                            pagination={false}
                            bordered={false}
                            size="middle"
                            style={{ marginTop: '24px' }}
                        />
                    </Card>
                </div>
            </div>
        </>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList
}))(TaskPage);
