import React from 'react';
import { Menu, Tooltip } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import styles from './RecordSider.module.less';

const RecordSider = ({
    recordList,
    currentIndex,
    onRecordChange,
    collapsed,
    onCollapsedChange
}) => {
    // 生成菜单项
    const menuItems = recordList.map((record, index) => ({
        key: `record_${index}`,
        label: (
            <span
                style={{
                    color: record.isValid === 1 ? 'red' : '',
                    textDecoration: record.isValid === 1 ? 'line-through' : ''
                }}
            >
                {index + 1}
            </span>
        )
    }));

    const handleMenuClick = ({ key }) => {
        const index = parseInt(key.replace('record_', ''), 10);
        onRecordChange(index);
    };

    return (
        <>
            {/* 折叠按钮 */}
            <div className={styles.collapseButton}>
                <Tooltip title={collapsed ? '展开记录列表' : '折叠记录列表'}>
                    {collapsed ? (
                        <MenuUnfoldOutlined
                            onClick={() => onCollapsedChange(false)}
                        />
                    ) : (
                        <MenuFoldOutlined
                            onClick={() => onCollapsedChange(true)}
                        />
                    )}
                </Tooltip>
            </div>

            {/* 侧边栏 */}
            {!collapsed && (
                <div className={styles.recordSider}>
                    <Menu
                        mode="inline"
                        selectedKeys={[`record_${currentIndex}`]}
                        onClick={handleMenuClick}
                        className={styles.recordMenu}
                        items={menuItems}
                    />
                </div>
            )}
        </>
    );
};

export default RecordSider;
