import { useState, useEffect, useCallback } from 'react';
import { Spin, message, Button, Modal, Input, Space, Card } from 'antd';
import { ExclamationCircleOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { getSpeedRoundList, updateSpeedRound, updateRecordCorrect } from 'COMMON/api/front_qe_tools/lazyperf';
import FrameViewer from './FrameViewer';
import styles from './PageCalibrate.module.less';

const { confirm } = Modal;
const { TextArea } = Input;

const PageCalibrate = ({ planId, caseNodeId, sceneId, recordList: propRecordList }) => {
    const [loading, setLoading] = useState(false);
    const [recordList, setRecordList] = useState(propRecordList || []);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [invalidModalVisible, setInvalidModalVisible] = useState(false);
    const [invalidComment, setInvalidComment] = useState('');

    // 获取数据列表
    const fetchRecordList = useCallback(async () => {
        if (!planId || !caseNodeId || !sceneId) {
            return;
        }

        setLoading(true);
        try {
            const response = await getSpeedRoundList({
                planId,
                caseNodeId,
                sceneId
            });

            if (response.code === 0) {
                setRecordList(response.data.recordList || []);
            } else {
                message.error(response.msg || '获取数据失败');
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    }, [planId, caseNodeId, sceneId]);

    // 初始化数据
    useEffect(() => {
        if (!propRecordList) {
            fetchRecordList();
        }
    }, [fetchRecordList, propRecordList]);

    // 切换到上一个视频记录
    const switchToPrevRecord = useCallback(() => {
        if (currentIndex === 0) {
            message.info('已到第一条');
        } else {
            setCurrentIndex(currentIndex - 1);
        }
    }, [currentIndex]);

    // 切换到下一个视频记录
    const switchToNextRecord = useCallback(() => {
        if (currentIndex === recordList.length - 1) {
            message.info('已到最后一条');
        } else {
            setCurrentIndex(currentIndex + 1);
        }
    }, [currentIndex, recordList.length]);

    // 当前记录
    const currentRecord = recordList[currentIndex];

    // 调用校准接口
    const callRecordCorrect = useCallback(async (record) => {
        if (!record) return;

            const correctDetail = record.correctDetail?.manual || {};

            const params = {
                recordSceneId: record.recordSceneId,
                correctDetail: {
                    firstFrameIndex: correctDetail.firstFrameIndex || 0,
                    firstFrameTimestamp: correctDetail.firstFrameTimestamp || 0,
                    lastFrameIndex: correctDetail.lastFrameIndex || 0,
                    lastFrameTimestamp: correctDetail.lastFrameTimestamp || 0
                }
            };

            await updateRecordCorrect(params);
        
    }, []);

    // 监听当前记录变化，调用校准接口
    useEffect(() => {
        if (currentRecord) {
            // 初始化进入分页校准时调用接口，表示用户已经 check 了
            callRecordCorrect(currentRecord);
        }
    }, [currentRecord, callRecordCorrect]);

    // 计算TTI - 使用旧版本的逻辑
    const calculateTTI = (record) => {
        if (!record || !record.frameList) return null;

        // 计算首尾帧索引
        let firstIndex =
            record.manualFirstStatus === 0 ? record.smartFirstFrame : record.manualFirstFrame;
        let lastIndex =
            record.manualLastStatus === 0 ? record.smartLastFrame : record.manualLastFrame;

        // 如果没有有效的帧索引，返回null
        if (
            firstIndex === undefined ||
            lastIndex === undefined ||
            !record.frameList[firstIndex] ||
            !record.frameList[lastIndex]
        ) {
            return null;
        }

        // 计算TTI
        const tti = record.frameList[lastIndex].timestamp - record.frameList[firstIndex].timestamp;

        if (tti < 0) {
            return { tti, isError: true, message: '首帧大于尾帧, 异常' };
        } else {
            return { tti, isError: false, message: `${tti} ms` };
        }
    };

    // 帧校准回调
    const handleFrameCorrect = useCallback(
        async (frameIndex, timestamp, type) => {
            if (!currentRecord) return;

            try {
                // 更新本地状态
                const updatedRecord = { ...currentRecord };
                if (type === 'first') {
                    updatedRecord.correctDetail.manual.firstFrameIndex = frameIndex;
                    updatedRecord.correctDetail.manual.firstFrameTimestamp = timestamp;
                    updatedRecord.correctDetail.manual.firstFrameStatus = 1;
                } else {
                    updatedRecord.correctDetail.manual.lastFrameIndex = frameIndex;
                    updatedRecord.correctDetail.manual.lastFrameTimestamp = timestamp;
                    updatedRecord.correctDetail.manual.lastFrameStatus = 1;
                }

                const newRecordList = [...recordList];
                newRecordList[currentIndex] = updatedRecord;
                setRecordList(newRecordList);

                // 用户双击操作修改首帧和尾帧时，调用校准接口
                await callRecordCorrect(updatedRecord);

                message.success(`${type === 'first' ? '首' : '尾'}帧校准成功`);
            } catch (error) {
                console.error('帧校准失败:', error);
                message.error('帧校准失败');
            }
        },
        [currentRecord, recordList, currentIndex, callRecordCorrect]
    );

    // 废弃记录
    const handleInvalidRecord = useCallback(async () => {
        if (!currentRecord) return;

        await updateSpeedRound({
            recordSceneId: currentRecord.recordSceneId,
            isValid: 1,
            validComment: invalidComment
        });

        // 更新本地状态
        const updatedRecord = { ...currentRecord };
        updatedRecord.isValid = 1;
        updatedRecord.invalidComment = invalidComment;

        const newRecordList = [...recordList];
        newRecordList[currentIndex] = updatedRecord;
        setRecordList(newRecordList);

        message.success('记录已废弃');
        setInvalidModalVisible(false);
        setInvalidComment('');
    }, [currentRecord, invalidComment, recordList, currentIndex]);

    // 恢复记录
    const handleRestoreRecord = useCallback(async () => {
        if (!currentRecord) return;

        try {
            const response = await updateSpeedRound({
                recordSceneId: currentRecord.recordSceneId,
                isValid: 0,
                validComment: ''
            });

            if (response.code === 0) {
                // 更新本地状态
                const updatedRecord = { ...currentRecord };
                updatedRecord.isValid = 0;
                updatedRecord.invalidComment = '';

                const newRecordList = [...recordList];
                newRecordList[currentIndex] = updatedRecord;
                setRecordList(newRecordList);

                message.success('记录已恢复');
            } else {
                message.error(response.msg || '恢复失败');
            }
        } catch (error) {
            console.error('恢复记录失败:', error);
            message.error('恢复记录失败');
        }
    }, [currentRecord, recordList, currentIndex]);

    // 显示废弃确认弹窗
    const showInvalidConfirm = () => {
        setInvalidModalVisible(true);
    };

    // 显示恢复确认弹窗
    const showRestoreConfirm = () => {
        confirm({
            title: '确认恢复记录？',
            icon: <ExclamationCircleOutlined />,
            content: '恢复后该记录将重新参与校准',
            onOk: handleRestoreRecord
        });
    };

    if (loading) {
        return (
            <div className={styles.pageCalibrate}>
                <Spin size="large" />
            </div>
        );
    }

    if (!recordList.length) {
        return (
            <div className={styles.pageCalibrate}>
                <Card>
                    <div className={styles.emptyState}>
                        <p>暂无数据</p>
                        <p>请先选择执行用例和执行场景</p>
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className={styles.pageCalibrate}>
            {/* 顶部操作栏 */}
            <div className={styles.topBar}>
                <div className={styles.leftInfo}>
                    <span className={styles.calibrateTitle}>人工校准</span>
                    {/* TTI时间显示 - 使用旧版本逻辑 */}
                    {(() => {
                        const ttiResult = calculateTTI(currentRecord);
                        if (!ttiResult) {
                            return <span className={styles.ttiInfo}>TTI: -- ms</span>;
                        }

                        if (ttiResult.isError) {
                            return (
                                <span className={styles.ttiInfo} style={{ color: '#ff0000' }}>
                                    {ttiResult.message}
                                </span>
                            );
                        } else {
                            return <span className={styles.ttiInfo}>TTI: {ttiResult.message}</span>;
                        }
                    })()}
                </div>
                <div className={styles.rightActions}>
                    <Space>
                        {/* 根据旧版本代码，这里应该是废弃/恢复按钮 */}
                        {currentRecord?.isValid === 0 ? (
                            <Button danger size="small" onClick={showInvalidConfirm}>
                                废弃
                            </Button>
                        ) : (
                            <Button type="primary" size="small" onClick={showRestoreConfirm}>
                                恢复
                            </Button>
                        )}
                    </Space>
                </div>
            </div>
            {/* 左侧记录分页 */}
            <div className={styles.recordSider}>
                <div className={styles.recordMenu}>
                    {recordList.map((record, index) => {
                        const isActive = index === currentIndex;
                        const isInvalid = record.isValid === 1;

                        return (
                            <div
                                key={index}
                                className={`${styles.recordItem} ${isActive ? styles.active : ''}`}
                                onClick={() => setCurrentIndex(index)}
                                title={
                                    isInvalid
                                        ? `记录已废弃${
                                              record.invalidComment
                                                  ? ': ' + record.invalidComment
                                                  : ''
                                          }`
                                        : `记录 ${index + 1}`
                                }
                            >
                                <span
                                    style={{
                                        color: isInvalid ? '#ff4d4f' : '',
                                        textDecoration: isInvalid ? 'line-through' : 'none'
                                    }}
                                >
                                    {index + 1}
                                </span>
                            </div>
                        );
                    })}
                </div>

                {/* 左侧分页器 - 按照旧版本设计 */}
                <div className={styles.siderPagination}>
                    <Button
                        disabled={currentIndex === 0}
                        className={styles.paginationBtn}
                        icon={<UpOutlined />}
                        onClick={() => currentIndex > 0 && setCurrentIndex(currentIndex - 1)}
                    />
                    <Input
                        className={styles.currentPage}
                        placeholder={`${currentIndex + 1}`}
                        onPressEnter={(e) => {
                            const value = parseInt(e.target.value, 10);
                            if (value && value > 0 && value <= recordList.length) {
                                setCurrentIndex(value - 1);
                            }
                        }}
                    />
                    <Button
                        disabled={currentIndex === recordList.length - 1}
                        className={styles.paginationBtn}
                        icon={<DownOutlined />}
                        onClick={() =>
                            currentIndex < recordList.length - 1 &&
                            setCurrentIndex(currentIndex + 1)
                        }
                    />
                    <p className={styles.totalInfo}>共 {recordList.length} 条</p>
                </div>
            </div>

            {/* 主内容区域 */}
            <div className={styles.mainContent}>
                {/* 帧校准组件 */}
                <div className={styles.frameViewerContainer}>
                    {currentRecord && (
                        <FrameViewer
                            record={currentRecord}
                            onFrameCorrect={handleFrameCorrect}
                            onSwitchToPrevRecord={switchToPrevRecord}
                            onSwitchToNextRecord={switchToNextRecord}
                        />
                    )}
                </div>
            </div>

            {/* 废弃记录弹窗 */}
            <Modal
                title="废弃记录"
                open={invalidModalVisible}
                onOk={handleInvalidRecord}
                onCancel={() => {
                    setInvalidModalVisible(false);
                    setInvalidComment('');
                }}
                okText="确认废弃"
                cancelText="取消"
            >
                <p>确认要废弃这条记录吗？废弃后该记录将不参与校准。</p>
                <TextArea
                    placeholder="请输入废弃原因（可选）"
                    value={invalidComment}
                    onChange={(e) => setInvalidComment(e.target.value)}
                    rows={3}
                />
            </Modal>
        </div>
    );
};

export default PageCalibrate;
