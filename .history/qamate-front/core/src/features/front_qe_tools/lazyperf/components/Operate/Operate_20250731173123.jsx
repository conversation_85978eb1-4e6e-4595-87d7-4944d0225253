import { Select, Button, message, Segmented, Form } from 'antd';
import { FormOutlined, AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import { useState, useEffect, useMemo, useRef } from 'react';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import deviceModel from 'COMMON/models/deviceModel';
import { FORM_ITEM_STYLE } from 'FEATURES/front_qe_tools/device/const';
import CalibrateTable from '../../FramePage/components/CalibrateTable';
import PageCalibrate from '../PageCalibrate';
import SlideCalibrate from '../SlideCalibrate';

const Operate = (props) => {
    const {
        setCurSceneId,
        caseNodeOptions,
        setCurCaseNodeId,
        sceneListOptions,
        recordList,
        planId,
        curCaseNodeId, // 从父组件接收当前选中的用例ID
        curSceneId // 从父组件接收当前选中的场景ID
    } = props;
    const [messageApi, contextHolder] = message.useMessage();
    const [searchForm] = Form.useForm();
    const [viewMode, setViewMode] = useState('List');

    // 当父组件的选中值改变时，同步更新表单
    useEffect(() => {
        searchForm.setFieldsValue({
            caseNodeIdList: curCaseNodeId,
            sceneIdList: curSceneId
        });
    }, [curCaseNodeId, curSceneId, searchForm]);

    return (
        <div>
            {contextHolder}
            <div style={{ display: 'flex', alignItems: 'center', minHeight: '42px' }}>
                <div style={{ flex: 1 }}>
                    {viewMode === 'List' ? (
                        <Form
                            form={searchForm}
                            name="basic"
                            layout="inline"
                            labelAlign="right"
                            labelCol={{ span: 8 }}
                            colon={false}
                            autoComplete="off"
                            style={{ marginBottom: 0 }}
                        >
                            <Form.Item
                                name="caseNodeIdList"
                                label="执行用例"
                                style={{ ...FORM_ITEM_STYLE, marginBottom: 0 }}
                            >
                                <Select
                                    placeholder="请选择执行用例"
                                    allowClear
                                    options={caseNodeOptions}
                                    value={curCaseNodeId}
                                    onChange={(value) => {
                                        setCurCaseNodeId(value);
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                name="sceneIdList"
                                label="执行场景"
                                style={{ ...FORM_ITEM_STYLE, marginBottom: 0 }}
                            >
                                <Select
                                    placeholder="请选择执行场景"
                                    allowClear
                                    options={sceneListOptions}
                                    value={curSceneId}
                                    onChange={(value) => {
                                        setCurSceneId(value);
                                    }}
                                />
                            </Form.Item>
                        </Form>
                    ) : null}
                </div>
                <Segmented
                    style={{ marginLeft: 'auto' }}
                    value={viewMode}
                    onChange={setViewMode}
                    options={[
                        {
                            label: '列表模式',
                            value: 'List',
                            icon: <BarsOutlined />
                        },
                        { label: '分页校准', value: 'Paging', icon: <FormOutlined /> },
                        {
                            label: '滑动校准',
                            value: 'Slide',
                            icon: <AppstoreOutlined />
                        }
                    ]}
                />
            </div>
            {viewMode === 'List' && <CalibrateTable recordList={recordList} />}
            {viewMode === 'Paging' && (
                <PageCalibrate
                    planId={planId}
                    caseNodeId={curCaseNodeId}
                    sceneId={curSceneId}
                    recordList={recordList}
                />
            )}
            {/* 这期不做 */}
            {/* {viewMode === 'Slide' && (
                <SlideCalibrate planId={planId} caseNodeId={curCaseNodeId} sceneId={curSceneId} />
            )} */}
        </div>
    );
};

export default connectModel([baseModel, deviceModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    userAccess: state.common.base.userAccess,
    poolList: state.common.device.poolList
}))(Operate);
