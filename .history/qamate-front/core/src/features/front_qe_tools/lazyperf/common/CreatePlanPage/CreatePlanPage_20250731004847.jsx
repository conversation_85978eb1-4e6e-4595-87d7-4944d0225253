import { useEffect, useState, useMemo, useRef } from 'react';
import { useNavigate } from 'umi';
import {
    Button,
    Card,
    Descriptions,
    Form,
    Input,
    message,
    Radio,
    Spin,
    Select,
    Tree,
    Tooltip,
    Badge,
    Tag
} from 'antd';
import { FilterOutlined, DownOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import electron from 'COMMON/utils/electron';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardContent } from 'COMMON/components/common/Card';
import { CardHeader } from 'COMMON/components/common/Card';
import { CardTitle } from 'COMMON/components/common/Card';
import NoContent from 'COMMON/components/common/NoContent';
import NodeIcon from 'COMMON/components/NewAddDropdown/components/NodeIcon';
import RenderTitle from 'FEATURES/front_qe_tools/plan/components/RenderTitle';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';
import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import { getStepListWithPath } from 'COMMON/api/front_qe_tools/step';
import { createPerfPlanList } from 'COMMON/api/front_qe_tools/lazyperf';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { getName } from 'COMMON/utils/stepUtils';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';
import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import EnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/EnvParams';
import CaseNodeTree from 'FEATURES/front_qe_tools/plan/components/Setting/CaseNodeTree';
import styles from './CreatePlanPage.module.less';
import { v4 as uuidv4 } from 'uuid';

const { Search } = Input;

// 扁平树为数组
function flattenTree(node, parentId = null, flattenedTree = []) {
    const nodeCopy = deepcopy(node);
    nodeCopy.parentId = parentId;
    flattenedTree.push(nodeCopy);

    const children = node.children || [];
    for (const child of children) {
        flattenTree(child, node.nodeId, flattenedTree);
    }

    return flattenedTree;
}

// 从树结构中提取选中的节点，并生成包含任务的节点ID列表
function getSelectedNodeKeys(tree, treeNodeList, nodeList = []) {
    tree.forEach((item) => {
        if (item.nodeType !== 1) {
            let node = treeNodeList?.find((v) => v.treeNodeId === item.nodeId);
            if (node) {
                node?.taskList?.forEach((task) => {
                    nodeList.push(item.nodeId + '-' + task.osType);
                });
            }
        } else {
            getSelectedNodeKeys(item.children, treeNodeList, nodeList);
        }
    });
    return nodeList;
}

// 策略配置选项
const STRATEGY_OPTIONS = {
    // 录制起始选项
    recordStart: [
        {
            value: 'case_0_start',
            label: (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Tag color="#722ed1">用例操作</Tag>
                    <span>执行开始</span>
                </div>
            ),
            color: '#52c41a',
            order: 0
        }
    ],
    // 首帧界定选项
    firstFrame: [],
    // 尾帧界定选项
    lastFrame: [
        {
            value: 'algorithm_0_crossdet',
            label: (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Tag color="#eb2f96">机制校准</Tag>
                    <span>控件识别</span>
                </div>
            ),
            color: '#eb2f96',
            order: -1
        }
    ]
};

// 获取策略的执行顺序
const getStrategyOrder = (strategy) => {
    // 查找策略在配置中的顺序
    const allStrategies = [
        ...STRATEGY_OPTIONS.recordStart,
        ...STRATEGY_OPTIONS.firstFrame,
        ...STRATEGY_OPTIONS.lastFrame
    ];
    const strategyConfig = allStrategies.find((item) => item.value === strategy);
    return strategyConfig ? strategyConfig.order : 999;
};

// 验证策略选择是否合理（首帧应该在录制起始之后或同时，尾帧应该在首帧之后或同时）
const validateStrategyOrder = (recordStart, firstFrame, lastFrame) => {
    const recordOrder = getStrategyOrder(recordStart);
    const firstOrder = getStrategyOrder(firstFrame);
    const lastOrder = getStrategyOrder(lastFrame);

    // 算法校准特殊处理
    if (firstFrame && firstFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以与任何录制起始配合
    }

    if (lastFrame && lastFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以作为任何尾帧
    }

    // 机制校准需要满足顺序关系
    if (recordStart && firstFrame && recordOrder > firstOrder) {
        return false;
    }

    if (firstFrame && lastFrame && firstOrder > lastOrder) {
        return false;
    }

    return true;
};

// 根据录制起始策略过滤可用的首帧策略
const getAvailableFirstFrameOptions = (recordStart) => {
    if (!recordStart) return STRATEGY_OPTIONS.firstFrame;

    const recordOrder = getStrategyOrder(recordStart);
    return STRATEGY_OPTIONS.firstFrame.filter((option) => {
        // 算法校准总是可用
        if (option.value.startsWith('algorithm_')) return true;

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= recordOrder;
    });
};

// 根据首帧策略过滤可用的尾帧策略
const getAvailableLastFrameOptions = (firstFrameStrategy) => {
    if (!firstFrameStrategy) return STRATEGY_OPTIONS.lastFrame;

    // 算法校准的首帧可以配合任何尾帧
    if (firstFrameStrategy.startsWith('algorithm_')) {
        return STRATEGY_OPTIONS.lastFrame;
    }

    const firstOrder = getStrategyOrder(firstFrameStrategy);
    return STRATEGY_OPTIONS.lastFrame.filter((option) => {
        // 算法校准总是可用
        if (option.value.startsWith('algorithm_')) return true;

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= firstOrder;
    });
};

function CreatePlanPage(props) {
    const { currentSpace, setDeviceList, setCurrentDevice, deviceList, currentDevice } = props;
    const query = getQueryParams();
    const [deviceType, setDeviceType] = useState('1');
    const [loading, setLoading] = useState(true);
    const [groupId, setGroupId] = useState(null);
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [stepInfoList, setStepInfoList] = useState([]);
    const [sceneListMap, setSceneListMap] = useState({});
    const [recordStartMap, setRecordStartMap] = useState({}); // 录制起始策略映射
    const [createLoading, setCreateLoading] = useState(false);
    const [groupList, setGroupList] = useState([]);
    const [envList, setEnvList] = useState([]); // 环境列表
    const [selectedNode, setSelectedNode] = useState([]);
    const [caseNodeList, setCaseNodeList] = useState([]);
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [nodeTreeMapList, setNodeTreeMapList] = useState([]);
    const [filteredList, setFilteredList] = useState([]);
    const [stepInfoMap, setStepInfoMap] = useState({}); // 存储每个用例的步骤信息
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [configForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const caseNodeTreeModalRef = useRef();
    const navigate = useNavigate();

    const handleChangeDeviceType = (e) => {
        const newDeviceType = e.target.value;
        setDeviceType(newDeviceType);
        // 同步更新表单值
        configForm.setFieldsValue({
            deviceType: newDeviceType
        });
        // 重置设备列表
        configForm.setFieldsValue({
            localDevice: []
        });

        // 重新生成过滤后的树形数据
        if (groupId && directoryTreeData.length > 0) {
            // 获取原始树数据并重新应用过滤
            getTreeNodeListWithPlanType(groupId, groupList, newDeviceType);
        }

        // 清空已选择的节点和用例列表，因为设备类型变化后之前的选择可能不再有效
        setSelectedNode([]);
        setCaseNodeList([]);
        configForm.setFieldValue('nodeTree', []);
    };

    // 添加场景
    const handleAddScene = (stepInfoIndex) => {
        const currentScenes = sceneListMap[stepInfoIndex] || [];
        const newScene = {
            id: uuidv4(),
            name: `场景 - ${currentScenes.length + 1}`,
            firstFrameStrategy: '', // 默认算法校准
            lastFrameStrategy: '', // 默认用例校准结束
            isDefault: false // 标记是否为默认场景
        };
        setSceneListMap({
            ...sceneListMap,
            [stepInfoIndex]: [...currentScenes, newScene]
        });
    };

    // 创建默认场景
    const createDefaultScene = () => {
        const defaultScene = {
            id: uuidv4(),
            name: '默认场景',
            firstFrameStrategy: '',
            lastFrameStrategy: '',
            isDefault: true // 标记为默认场景，不可删除
        };
        return defaultScene;
    };

    // 获取树形数据
    const getTreeNodeListWithPlanType = async (groupId, _groupList, newDeviceType) => {
        try {
            setLoading(true);
            if (!groupId) {
                return;
            }
            let { tree } = await getTreeNodeList({ groupId: groupId });

            // 使用当前设备类型过滤树形数据
            const filteredTree = getNewTree(tree, newDeviceType || deviceType);
            setDirectoryTreeData(filteredTree);
            setSearchValue('');

            // 基于过滤后的树生成节点映射
            let nodeKeys = filteredTree.map((item) => flattenTree(item)).flat() ?? [];
            setNodeTreeMapList(nodeKeys);
            // 获取 创建第一次进入默认全部key
            setExpandedKeys(nodeKeys.map((item) => item.nodeId));
            setLoading(false);
        } catch (err) {
            console.error('获取树形数据失败:', err);
            setLoading(false);
        }
    };

    // 处理树形数据，为每个用例节点添加操作系统类型的子节点
    // 根据当前选择的设备类型过滤显示对应的用例
    const getNewTree = (tree, currentDeviceType = deviceType) => {
        const targetOsType = parseInt(currentDeviceType); // 1: Android, 2: iOS

        return tree.map((item) => {
            if (item.nodeType === 2) {
                let _osTypeList;
                if (item.signType === 1 && item.osTypeList[0] === 3) {
                    _osTypeList = [3];
                } else if (item.signType === 1 && item.osTypeList.length > 1) {
                    _osTypeList = [item.osTypeList[0]];
                } else if (item.signType === 0 && item.osTypeList[0] === 3) {
                    _osTypeList = [1, 2];
                } else if (item.signType === 0 && item.osTypeList.length > 1) {
                    _osTypeList = item.osTypeList;
                } else {
                    _osTypeList = item.osTypeList;
                }

                // 根据当前设备类型过滤操作系统类型
                const filteredOsTypeList = _osTypeList.filter(os => {
                    // 如果是服务端用例(osType=3)，总是显示
                    if (os === 3) return true;
                    // 否则只显示与当前设备类型匹配的用例
                    return os === targetOsType;
                });

                // 只有当过滤后的列表不为空时才创建子节点
                if (filteredOsTypeList.length > 0) {
                    item.children = filteredOsTypeList.map((os) => ({
                        nodeId: item.nodeId + '-' + os,
                        os: os,
                        caseRootId: item.caseRootId,
                        caseNodeList: [],
                        signType: item.signType
                    }));
                    return item;
                } else {
                    // 如果没有匹配的用例类型，返回null，后续会被过滤掉
                    return null;
                }
            } else {
                // 递归处理子节点
                const processedChildren = getNewTree(item.children, currentDeviceType);
                // 过滤掉null值（没有匹配用例的节点）
                const validChildren = processedChildren.filter(child => child !== null);

                // 如果处理后的子节点为空，且当前节点是目录节点，则不显示该目录
                if (item.nodeType === 1 && validChildren.length === 0) {
                    return null;
                }

                return { ...item, children: validChildren };
            }
        }).filter(item => item !== null); // 过滤掉null值
    };

    // 树展开处理
    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };

    // 搜索处理
    const onSearchValueChange = (e) => {
        const { value } = e.target;
        const newExpandedKeys = nodeTreeMapList
            .map((item) => {
                if (`${item?.nodeName || ''}`.includes(value)) {
                    return item?.nodeId || null;
                }
                return null;
            })
            .filter((item) => item !== null);
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(true);
        setSearchValue(value);
    };

    // 获取步骤类型的颜色和分类
    const getStepTypeInfo = (stepType) => {
        // 人工操作
        if ([101, 102].includes(stepType)) {
            return { color: '#52c41a', category: '人工操作' };
        }

        // 控件操作
        if (stepType === 201) {
            return { color: '#1890ff', category: '控件操作' };
        }

        // 系统操作
        if (
            [
                401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417,
                418, 419, 422, 423, 424, 225, 430
            ].includes(stepType)
        ) {
            return { color: '#fa8c16', category: '系统操作' };
        }

        // 校验操作
        if ([501, 502, 503, 504, 601, 602].includes(stepType)) {
            return { color: '#eb2f96', category: '校验操作' };
        }

        // 接口操作
        if ([1001, 1101, 1201].includes(stepType)) {
            return { color: '#13c2c2', category: '接口操作' };
        }

        // 步骤组
        if (stepType === 1401) {
            return { color: '#722ed1', category: '步骤组' };
        }

        // 默认
        return { color: '#d9d9d9', category: '其他操作' };
    };

    // 获取用例的步骤信息
    const getStepInfoForCase = async (caseNodeId, osType) => {
        try {
            const cacheKey = `${caseNodeId}-${osType}`;
            if (stepInfoMap[cacheKey]) {
                return stepInfoMap[cacheKey];
            }

            const { caseNodeList } = await getStepListWithPath({
                caseNodeId: caseNodeId,
                osType: osType,
                withInfo: true
            });

            console.log('获取到的用例节点列表:', caseNodeList);

            const stepOptions = [];
            for (let node of caseNodeList) {
                let stepList = node?.extra?.stepInfo?.[convertOsTypeToType(osType)];
                console.log(`节点 ${node.caseNodeId} 的步骤列表:`, stepList);

                if (stepList && Array.isArray(stepList)) {
                    stepList.forEach((step, index) => {
                        const stepTypeInfo = getStepTypeInfo(step.stepType);
                        const stepName = getName(step.stepType);
                        const stepDesc = step.stepDesc || stepName;

                        console.log(`步骤 ${index + 1}:`, {
                            stepType: step.stepType,
                            stepName,
                            stepDesc,
                            stepTypeInfo
                        });

                        stepOptions.push({
                            value: `steps_${index}_start`,
                            label: stepName,
                            desc: stepDesc,
                            stepId: step.stepId,
                            stepType: step.stepType,
                            category: stepTypeInfo.category,
                            color: stepTypeInfo.color,
                            stepIndex: index + 1
                        });
                    });
                }
            }

            console.log('生成的步骤选项:', stepOptions);

            // 缓存结果
            setStepInfoMap((prev) => ({
                ...prev,
                [cacheKey]: stepOptions
            }));

            return stepOptions;
        } catch (error) {
            console.error('获取步骤信息失败:', error);
            return [];
        }
    };

    // 生成用例组选项
    const planTypeOptions = () => {
        return filteredList.map((group) => ({
            value: group.groupId,
            key: group.groupId,
            label: `源于${group.groupName}`
        }));
    };

    // 递归过滤隐藏菜单
    const treeData = useMemo(() => {
        const loop = (data) => {
            let newData = [];
            data.forEach((item) => {
                const strTitle = !item?.nodeName || item?.nodeName === null ? '' : item.nodeName;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const title =
                    index > -1 ? (
                        <span>
                            {beforeStr}
                            <span className={styles.highlight}>{searchValue}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span>{strTitle}</span>
                    );
                if (item?.nodeType === 1) {
                    newData.push({
                        title: (
                            <>
                                <NodeIcon type={item.nodeType} />
                                {title}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId,
                        children: loop(item.children)
                    });
                } else if (item?.nodeType === 2) {
                    newData.push({
                        title: (
                            <>
                                <NodeIcon type={item.nodeType} />
                                {title}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId,
                        children: loop(item.children)
                    });
                } else if (item.os) {
                    let curNodeList =
                        caseNodeList?.find(
                            (list) => list?.nodeId === item?.nodeId && item?.os === list?.osType
                        )?.caseIdList ?? [];
                    newData.push({
                        title: (
                            <>
                                <RenderTitle os={item.os} />
                                {selectedNode.includes(item.nodeId) && (
                                    <Tooltip
                                        title="筛选需要创建的用例, 默认全不选"
                                        placement="right"
                                    >
                                        <Badge dot={curNodeList?.length > 0}>
                                            <FilterOutlined
                                                className={styles.filter}
                                                onClick={() => {
                                                    caseNodeTreeModalRef?.current?.show(
                                                        item.caseRootId,
                                                        item.os,
                                                        +item.nodeId.split('-')?.[0],
                                                        caseNodeList
                                                    );
                                                }}
                                            />
                                        </Badge>
                                    </Tooltip>
                                )}
                            </>
                        ),
                        key: item.nodeId,
                        value: item.nodeId
                    });
                }
            });
            return newData;
        };
        console.log('lazyperf treeData', directoryTreeData);
        return loop(directoryTreeData);
    }, [searchValue, directoryTreeData, selectedNode, caseNodeList, deviceType]);

    // 删除场景
    const handleDeleteScene = (stepInfoIndex, sceneId) => {
        const currentScenes = sceneListMap[stepInfoIndex] || [];
        const sceneToDelete = currentScenes.find((scene) => scene.id === sceneId);

        // 防止删除默认场景
        if (sceneToDelete && sceneToDelete.isDefault) {
            message.warning('默认场景不能删除');
            return;
        }

        // 防止删除最后一个场景
        if (currentScenes.length <= 1) {
            message.warning('至少需要保留一个场景');
            return;
        }

        setSceneListMap({
            ...sceneListMap,
            [stepInfoIndex]: currentScenes.filter((scene) => scene.id !== sceneId)
        });
    };

    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            console.log('groupList', groupList);
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            // 设置过滤后的列表（这里可以根据需要进行过滤）
            setFilteredList(groupList);
            // 默认选中第一项
            if (groupList.length > 0) {
                const firstGroupId = groupList[0]?.groupId;
                setGroupId(firstGroupId);
                getTreeNodeListWithPlanType(firstGroupId, groupList);
            }
        }
        func();
    }, [currentSpace?.id, query?.planType]);

    useEffect(() => {
        obtaionEnvParamsList();
        // 初始化表单的设备类型值
        configForm.setFieldsValue({
            deviceType: '1'
        });
    }, []);

    // 监听 caseNodeList 变化，生成对应的 stepInfoList
    useEffect(() => {
        const updateStepInfoList = async () => {
            console.log('caseNodeList changed:', caseNodeList);
            const newStepInfoList = await generateStepInfoFromCaseNodeList();
            console.log('generated stepInfoList:', newStepInfoList);
            setStepInfoList(newStepInfoList);

            // 为新的步骤信息初始化场景映射和录制起始映射
            const newSceneListMap = {};
            const newRecordStartMap = {};

            newStepInfoList.forEach((step, index) => {
                // 检查是否已有该用例的配置
                const existingSceneIndex = Object.keys(sceneListMap).find((key) => {
                    const existingStep = stepInfoList[key];
                    return existingStep && existingStep.caseNodeId === step.caseNodeId;
                });

                if (existingSceneIndex && sceneListMap[existingSceneIndex]) {
                    // 保留已有的场景数据
                    newSceneListMap[index] = sceneListMap[existingSceneIndex];
                    newRecordStartMap[index] = recordStartMap[existingSceneIndex] || 'case_0_start';
                } else {
                    // 新用例创建默认场景
                    const defaultScene = createDefaultScene();
                    newSceneListMap[index] = [defaultScene];
                    newRecordStartMap[index] = 'case_0_start';
                }
            });

            setSceneListMap(newSceneListMap);
            setRecordStartMap(newRecordStartMap);
        };

        updateStepInfoList();
    }, [caseNodeList]);

    // 获取可用的录制起始选项（基于步骤信息）
    const getRecordStartOptions = (stepInfo) => {
        // 从基础选项开始
        const options = [...STRATEGY_OPTIONS.recordStart];

        if (stepInfo && stepInfo.length > 0) {
            stepInfo.forEach((step) => {
                options.push({
                    value: step.value,
                    label: (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Tag color={step.color}>{step.category}</Tag>
                            <span>{step.label}</span>
                        </div>
                    ),
                    desc: step.desc
                });
            });
        }

        return options;
    };

    // 获取可用的首帧界定选项（基于步骤信息）
    const getFirstFrameOptionsFromSteps = (stepInfo) => {
        const options = [];

        if (stepInfo && stepInfo.length > 0) {
            stepInfo.forEach((step) => {
                options.push({
                    value: step.value,
                    label: (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Tag color={step.color}>{step.category}</Tag>
                            <span>{step.label}</span>
                        </div>
                    ),
                    desc: step.desc
                });
            });
        }

        return options;
    };

    // 获取可用的尾帧界定选项（基于步骤信息）
    const getLastFrameOptionsFromSteps = (stepInfo) => {
        const options = [];

        if (stepInfo && stepInfo.length > 0) {
            stepInfo.forEach((step) => {
                options.push({
                    value: step.value.replace('_start', '_end'),
                    label: (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Tag color={step.color}>{step.category}</Tag>
                            <span>{step.label}</span>
                        </div>
                    ),
                    desc: step.desc + ' 结束'
                });
            });
        }

        return options;
    };

    // 监听表单值变化，同步更新 deviceType 状态
    useEffect(() => {
        const deviceTypeValue = configForm.getFieldValue('deviceType');
        if (deviceTypeValue && deviceTypeValue !== deviceType) {
            setDeviceType(deviceTypeValue);
        }
    }, [configForm]);

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        try {
            if (!currentSpace?.id) {
                return;
            }
            let androidRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 1
            });
            let iosRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 2
            });
            let serverRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 4
            });
            setEnvList({
                android: androidRes.envList,
                ios: iosRes.envList,
                server: serverRes?.envList
            });
        } catch (err) {
            message.error(err?.message ?? err);
        }
    };

    // 从树形数据中获取节点的完整路径名称
    const getNodePathFromTree = (nodeId) => {
        const findNodePath = (nodes, targetId, currentPath = []) => {
            for (const node of nodes) {
                const newPath = [...currentPath, node.nodeName || `节点-${node.nodeId}`];
                if (node.nodeId === targetId) {
                    return newPath;
                }
                if (node.children && node.children.length > 0) {
                    const found = findNodePath(node.children, targetId, newPath);
                    if (found) return found;
                }
            }
            return null;
        };
        const path = findNodePath(directoryTreeData, nodeId);
        return path ? path.join(' > ') : `节点-${nodeId}`;
    };

    // 根据 caseNodeList 生成 stepInfoList
    const generateStepInfoFromCaseNodeList = async () => {
        const stepInfo = [];

        for (const caseItem of caseNodeList) {
            if (caseItem.caseIdList && caseItem.caseIdList.length > 0) {
                // 获取节点的完整路径名称
                const nodePath = getNodePathFromTree(caseItem.nodeId);
                const osTypeName =
                    caseItem.osType === 1 ? 'Android' : caseItem.osType === 2 ? 'iOS' : 'Server';

                // 为每个选中的用例ID生成步骤信息
                for (const caseId of caseItem.caseIdList) {
                    try {
                        // 获取用例的步骤信息
                        const stepOptions = await getStepInfoForCase(caseId, caseItem.osType);

                        // 构建用例路径名称：完整节点路径 > 操作系统 > 用例名称
                        const caseNodeName = `${nodePath} > ${osTypeName} > 用例叶子节点 ID-${caseId}`;

                        stepInfo.push({
                            caseNodeId: caseId,
                            caseNodeName: caseNodeName,
                            nodeId: caseItem.nodeId,
                            osType: caseItem.osType,
                            caseRootId: caseItem.caseRootId,
                            stepInfo: stepOptions // 包含实际的步骤信息
                        });
                    } catch (error) {
                        console.error(`获取用例 ${caseId} 的步骤信息失败:`, error);
                        // 如果获取步骤信息失败，仍然添加基本信息
                        const caseNodeName = `${nodePath} > ${osTypeName} > 用例-${caseId}`;
                        stepInfo.push({
                            caseNodeId: caseId,
                            caseNodeName: caseNodeName,
                            nodeId: caseItem.nodeId,
                            osType: caseItem.osType,
                            caseRootId: caseItem.caseRootId,
                            stepInfo: []
                        });
                    }
                }
            }
        }
        return stepInfo;
    };

    const handleCreatePlan = () => {
        configForm
            ?.validateFields()
            .then(async (values) => {
                // 使用 stepInfoList 生成用例参数
                const caseNodeListForSubmit = stepInfoList.map((stepInfo, index) => {
                    const scenes = sceneListMap[index] || [];
                    return {
                        caseNodeId: stepInfo.caseNodeId,
                        recordStartStrategy: recordStartMap[index] || 'case_0_start',
                        sceneList: scenes.map((scene) => ({
                            name: scene.name,
                            firstFrameStrategy: scene.firstFrameStrategy,
                            lastFrameStrategy: scene.lastFrameStrategy
                        }))
                    };
                });

                // 获取第一个用例的 nodeId 作为 treeNodeId（去掉操作系统后缀）
                const firstStepInfo = stepInfoList[0];
                const treeNodeId = firstStepInfo
                    ? parseInt(firstStepInfo.nodeId.toString().split('-')[0])
                    : null;

                let body = {
                    moduleId: currentSpace?.id,
                    name: values.name,
                    type: +values.type,
                    // 用例参数
                    caseNodeParams: {
                        treeNodeId: treeNodeId, // 从选中的用例中获取 treeNodeId
                        osType: +values.deviceType,
                        caseNodeList: caseNodeListForSubmit
                    },
                    // 执行参数
                    cloudParams: {
                        type: +values.deviceType === 1 ? 8 : 9, // 8-Android 速度评测任务 9-IOS 速度评测任务
                        deviceId: values.localDevice?.[0] || null, // 从表单的 localDevice 字段获取设备ID
                        executeTimes: +values.executeTimes,
                        envParams: (() => {
                            const envParamsData =
                                +values.deviceType === 1
                                    ? createPlanOption.executeConfig.android.envParams
                                    : createPlanOption.executeConfig.ios.envParams;

                            // 如果有 envId，按照接口文档格式传递
                            if (envParamsData?.envId) {
                                return {
                                    envId: envParamsData.envId
                                };
                            }

                            // 如果没有 envId 但有环境详情，使用 envDetail 格式
                            if (
                                envParamsData?.paramList ||
                                envParamsData?.appList ||
                                envParamsData?.serverList
                            ) {
                                return {
                                    envDetail: {
                                        paramList: (envParamsData.paramList || []).map((item) => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue || item.paramValue
                                        })),
                                        appList: (envParamsData.appList || []).map((item) => ({
                                            appId: item.appId,
                                            envValue: item.envValue || item.packageList?.[0]
                                        })),
                                        serverList: (envParamsData.serverList || []).map(
                                            (item) => ({
                                                serverId: item.serverId,
                                                envValue: item.envValue || item.addressList?.[0]
                                            })
                                        )
                                    }
                                };
                            }

                            // 默认返回空的 envDetail
                            return {
                                envDetail: {
                                    paramList: [],
                                    appList: [],
                                    serverList: []
                                }
                            };
                        })()
                    }
                };

                console.log('创建计划的请求体:', body);
                setCreateLoading(true);
                createPerfPlanList(body)
                    .then((_res) => {
                        messageApi.success('任务创建成功');
                        EventBus.emit('refreshPerfPlanList');
                        navigate(
                            stringifyUrl({
                                url: '/lazyperf/index',
                                query: {
                                    moduleId: currentSpace?.id,
                                    planId: 111,
                                    planType: 1,
                                    stage: 'task'
                                }
                            })
                        );
                        setCreateLoading(false);
                    })
                    .catch(() => {
                        setCreateLoading(false);
                    });
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setCreateLoading(false);
                messageApi.warning('请填写完整表单');
            });
    };

    // 获取设备列表
    useEffect(() => {
        if (globalThis.isElectron && globalThis.isElectron()) {
            let _osType = +deviceType;
            electron
                .send('device.list', { deviceType: 2 === _osType ? 'iOS' : 'android' })
                .then((res) => {
                    let newDeviceList = { ...deviceList };
                    newDeviceList[2 === _osType ? 'iOS' : 'android'] = res;
                    setDeviceList(newDeviceList);
                    let curDevice = !isEmpty(res) ? res[0] : null;
                    for (let item of res) {
                        if (item.status !== 5) {
                            curDevice = item;
                            break;
                        }
                    }
                    if (currentDevice === null) {
                        setCurrentDevice(curDevice);
                    }
                });
        }
    }, [deviceType]);

    useEffect(() => {
        const handler = ({ deviceType, deviceId, deviceStatus, statusStage }) => {
            for (let device of deviceList[deviceType]) {
                if (device.deviceId === deviceId) {
                    device.status = deviceStatus;
                    device.statusStage = statusStage;
                }
                if (currentDevice?.deviceId === device.deviceId && device.deviceId === deviceId) {
                    setCurrentDevice(device);
                }
            }
            setDeviceList(deviceList);
        };
        if (globalThis.isElectron && globalThis.isElectron()) {
            electron.on('device.update', handler);
            return () => electron.remove('device.update', handler);
        }
    }, [deviceList, currentDevice?.deviceId]);

    console.log('stepInfoList', stepInfoList);
    console.log('deviceType', deviceType);
    console.log('envList', envList);
    console.log('envList for current deviceType', envList?.[+deviceType === 2 ? 'ios' : 'android']);

    return (
        <Spin spinning={createLoading}>
            <CardContent>
                {contextHolder}
                <Descriptions column={1} size="small" />
                <div className={styles.header}>
                    <CardHeader
                        text={'新建计划'}
                        extra={
                            <>
                                <Button type="primary" onClick={handleCreatePlan}>
                                    创建
                                </Button>
                                <Button
                                    onClick={() => {
                                        navigate(-1);
                                    }}
                                >
                                    取消
                                </Button>
                            </>
                        }
                    />
                </div>
                <div className={styles.container}>
                    <Form form={configForm} layout="horizontal" requiredMark={false} colon={false}>
                        <CardTitle text="任务配置" />
                        <Form.Item name="name" label="任务名称">
                            <Input placeholder="请输入任务名称" allowClear />
                        </Form.Item>
                        <Form.Item name="type" label="评测类型" initialValue={'1'.toString()}>
                            <Radio.Group>
                                <Radio value="1">速度评测</Radio>
                                <Radio value="2">指标评测</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <CardTitle text="设备配置" />
                        <Form.Item name="deviceType" label="设备类型" initialValue={'1'}>
                            <Radio.Group onChange={handleChangeDeviceType}>
                                <Radio value="1">Android</Radio>
                                <Radio value="2">iOS</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <LocalDevice form={configForm} osType={deviceType} />
                        <RetryTimes
                            form={configForm}
                            label="执行次数"
                            min={1}
                            max={99}
                            name="executeTimes"
                            initialValue={1}
                            required
                            tooltip="最大执行次数，范围：1-99"
                            placeholder="请设置执行次数"
                        />
                        <EnvParams
                            form={configForm}
                            osType={+deviceType}
                            envList={envList?.[+deviceType === 2 ? 'ios' : 'android'] || []}
                            createPlanOption={createPlanOption}
                            setCreatePlanOption={setCreatePlanOption}
                        />
                        <CardTitle text="测试用例">
                            <Select
                                variant="borderless"
                                style={{
                                    width: 161
                                }}
                                className={styles.selecPlanType}
                                popupMatchSelectWidth={false}
                                size="small"
                                value={groupId}
                                suffixIcon={<DownOutlined />}
                                options={planTypeOptions()}
                                onChange={(value) => {
                                    setGroupId(value);
                                    getTreeNodeListWithPlanType(value, groupList);
                                }}
                            />
                        </CardTitle>
                        <div className={styles.cardLayout}>
                            <Form.Item
                                name="nodeTree"
                                shouldUpdate={() => false}
                                required
                                rules={[
                                    () => ({
                                        validator() {
                                            if (selectedNode && selectedNode.length > 0) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(
                                                new Error('请选择需要测试的用例')
                                            );
                                        }
                                    })
                                ]}
                            >
                                <div className={styles.nodeTree}>
                                    <Search
                                        className={styles.search}
                                        value={searchValue}
                                        placeholder="请输入要检索的内容"
                                        onChange={(e) => onSearchValueChange(e)}
                                    />
                                    <Spin spinning={loading}>
                                        <div
                                            className={styles.tree}
                                            style={{
                                                maxHeight: 300,
                                                overflowY: 'auto',
                                                border: '1px solid #d9d9d9',
                                                borderRadius: '6px',
                                                padding: '8px'
                                            }}
                                        >
                                            {treeData?.length ? (
                                                <Tree
                                                    onExpand={onExpand}
                                                    checkable
                                                    showLine
                                                    autoExpandParent={autoExpandParent}
                                                    expandedKeys={expandedKeys}
                                                    treeData={treeData}
                                                    checkedKeys={selectedNode}
                                                    onCheck={(keys, { checkedNodes }) => {
                                                        setSelectedNode(
                                                            checkedNodes.map((item) => item.key)
                                                        );
                                                        let newCaseNodeList = [];
                                                        keys = keys.filter((item) =>
                                                            (item + '').includes('-')
                                                        );
                                                        for (let item of caseNodeList) {
                                                            if (
                                                                keys.includes(
                                                                    item.nodeId + '-' + item.osType
                                                                )
                                                            ) {
                                                                newCaseNodeList.push(item);
                                                            }
                                                        }
                                                        setCaseNodeList(newCaseNodeList);
                                                        configForm.setFieldValue(
                                                            'nodeTree',
                                                            checkedNodes.map((item) => item.key)
                                                        );
                                                    }}
                                                />
                                            ) : (
                                                <NoContent
                                                    text="暂无集成回归用例"
                                                    className={styles.noContent}
                                                />
                                            )}
                                        </div>
                                    </Spin>
                                </div>
                            </Form.Item>
                        </div>

                        {/* 当选择执行用例后显示的场景配置 */}
                        {stepInfoList && stepInfoList.length > 0 && (
                            <>
                                {stepInfoList.map((stepInfo, index) => {
                                    console.log('stepInfo', stepInfo);
                                    return (
                                        <Card
                                            key={stepInfo.caseNodeId}
                                            title={`${stepInfo.caseNodeName || `用例${index + 1}`}`}
                                            size="small"
                                            style={{ marginBottom: '24px' }}
                                        >
                                            {/* 录制起始配置 */}
                                            <div style={{ marginBottom: '16px' }}>
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: '8px',
                                                        marginBottom: '12px'
                                                    }}
                                                >
                                                    <label
                                                        style={{
                                                            fontWeight: 'bold',
                                                            minWidth: '80px',
                                                            textAlign: 'right'
                                                        }}
                                                    >
                                                        录制起始:
                                                    </label>
                                                    <Select
                                                        style={{ width: '200px' }}
                                                        placeholder="请选择录制起始策略"
                                                        value={
                                                            recordStartMap[index] || 'case_0_start'
                                                        }
                                                        onChange={(value) => {
                                                            // 更新录制起始策略
                                                            setRecordStartMap({
                                                                ...recordStartMap,
                                                                [index]: value
                                                            });

                                                            // 检查并调整已有场景的首帧策略
                                                            const currentScenes =
                                                                sceneListMap[index] || [];
                                                            const updatedScenes = currentScenes.map(
                                                                (scene) => {
                                                                    const updatedScene = {
                                                                        ...scene
                                                                    };

                                                                    // 如果当前首帧策略与新的录制起始不兼容，自动调整
                                                                    if (
                                                                        scene.firstFrameStrategy &&
                                                                        !validateStrategyOrder(
                                                                            value,
                                                                            scene.firstFrameStrategy,
                                                                            scene.lastFrameStrategy
                                                                        )
                                                                    ) {
                                                                        // 找到第一个可用的首帧策略
                                                                        const availableOptions =
                                                                            getAvailableFirstFrameOptions(
                                                                                value
                                                                            );
                                                                        if (
                                                                            availableOptions.length >
                                                                            0
                                                                        ) {
                                                                            updatedScene.firstFrameStrategy =
                                                                                availableOptions[0].value;

                                                                            // 同时检查尾帧策略是否需要调整
                                                                            if (
                                                                                scene.lastFrameStrategy &&
                                                                                !validateStrategyOrder(
                                                                                    value,
                                                                                    updatedScene.firstFrameStrategy,
                                                                                    scene.lastFrameStrategy
                                                                                )
                                                                            ) {
                                                                                const availableLastOptions =
                                                                                    getAvailableLastFrameOptions(
                                                                                        updatedScene.firstFrameStrategy
                                                                                    );
                                                                                if (
                                                                                    availableLastOptions.length >
                                                                                    0
                                                                                ) {
                                                                                    updatedScene.lastFrameStrategy =
                                                                                        availableLastOptions[0].value;
                                                                                }
                                                                            }
                                                                        }
                                                                    }

                                                                    return updatedScene;
                                                                }
                                                            );

                                                            if (updatedScenes.length > 0) {
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }
                                                        }}
                                                        options={getRecordStartOptions(
                                                            stepInfo.stepInfo
                                                        )}
                                                    />
                                                </div>
                                            </div>

                                            {/* 场景列表 */}
                                            {(sceneListMap[index] || []).map((scene) => (
                                                <Card
                                                    key={scene.id}
                                                    size="small"
                                                    style={{
                                                        marginBottom: '12px',
                                                        position: 'relative'
                                                    }}
                                                    extra={
                                                        // 只有非默认场景且场景数量大于1时才显示删除按钮
                                                        !scene.isDefault &&
                                                        (sceneListMap[index] || []).length > 1 ? (
                                                            <Button
                                                                type="text"
                                                                danger
                                                                size="small"
                                                                onClick={() =>
                                                                    handleDeleteScene(
                                                                        index,
                                                                        scene.id
                                                                    )
                                                                }
                                                                style={{
                                                                    fontSize: '12px',
                                                                    padding: '4px 8px',
                                                                    height: 'auto'
                                                                }}
                                                            >
                                                                删除
                                                            </Button>
                                                        ) : null
                                                    }
                                                >
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '8px',
                                                            marginBottom: '16px'
                                                        }}
                                                    >
                                                        <label
                                                            style={{
                                                                fontWeight: 'bold',
                                                                minWidth: '80px',
                                                                textAlign: 'right'
                                                            }}
                                                        >
                                                            场景名称:
                                                        </label>
                                                        <Input
                                                            style={{ flex: 1 }}
                                                            placeholder="请输入场景名称"
                                                            value={scene.name || ''}
                                                            onChange={(e) => {
                                                                const updatedScenes = sceneListMap[
                                                                    index
                                                                ].map((s) =>
                                                                    s.id === scene.id
                                                                        ? {
                                                                              ...s,
                                                                              name: e.target.value
                                                                          }
                                                                        : s
                                                                );
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }}
                                                        />
                                                    </div>

                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '8px',
                                                            marginBottom: '16px'
                                                        }}
                                                    >
                                                        <label
                                                            style={{
                                                                fontWeight: 'bold',
                                                                minWidth: '80px',
                                                                textAlign: 'right'
                                                            }}
                                                        >
                                                            首帧界定:
                                                        </label>
                                                        <Select
                                                            className={styles.selecPlanType}
                                                            style={{ flex: 1 }}
                                                            placeholder="请选择首帧界定策略"
                                                            value={
                                                                scene.firstFrameStrategy ||
                                                                undefined
                                                            }
                                                            onChange={(value) => {
                                                                const recordStart =
                                                                    recordStartMap[index] ||
                                                                    'case_0_start';

                                                                // 验证策略选择是否合理
                                                                if (
                                                                    !validateStrategyOrder(
                                                                        recordStart,
                                                                        value,
                                                                        scene.lastFrameStrategy
                                                                    )
                                                                ) {
                                                                    message.warning(
                                                                        '首帧策略不能早于录制起始策略执行'
                                                                    );
                                                                    return;
                                                                }

                                                                const updatedScenes = sceneListMap[
                                                                    index
                                                                ].map((s) => {
                                                                    if (s.id === scene.id) {
                                                                        const updatedScene = {
                                                                            ...s,
                                                                            firstFrameStrategy:
                                                                                value
                                                                        };

                                                                        // 如果首帧策略变化导致尾帧策略不兼容，自动调整尾帧策略
                                                                        if (
                                                                            s.lastFrameStrategy &&
                                                                            !validateStrategyOrder(
                                                                                recordStart,
                                                                                value,
                                                                                s.lastFrameStrategy
                                                                            )
                                                                        ) {
                                                                            const availableLastOptions =
                                                                                getAvailableLastFrameOptions(
                                                                                    value
                                                                                );
                                                                            if (
                                                                                availableLastOptions.length >
                                                                                0
                                                                            ) {
                                                                                updatedScene.lastFrameStrategy =
                                                                                    availableLastOptions[0].value;
                                                                            }
                                                                        }
                                                                        return updatedScene;
                                                                    }
                                                                    return s;
                                                                });
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }}
                                                            options={[
                                                                ...getAvailableFirstFrameOptions(
                                                                    recordStartMap[index] ||
                                                                        'case_0_start'
                                                                ),
                                                                ...getFirstFrameOptionsFromSteps(
                                                                    stepInfo.stepInfo
                                                                )
                                                            ].filter(
                                                                (option, index, self) =>
                                                                    index ===
                                                                    self.findIndex(
                                                                        (o) =>
                                                                            o.value === option.value
                                                                    )
                                                            )}
                                                        />
                                                    </div>

                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '8px',
                                                            marginBottom: '16px'
                                                        }}
                                                    >
                                                        <label
                                                            style={{
                                                                fontWeight: 'bold',
                                                                minWidth: '80px',
                                                                textAlign: 'right'
                                                            }}
                                                        >
                                                            尾帧界定:
                                                        </label>
                                                        <Select
                                                            className={styles.selecPlanType}
                                                            style={{ flex: 1 }}
                                                            placeholder="请选择尾帧界定策略"
                                                            value={
                                                                scene.lastFrameStrategy || undefined
                                                            }
                                                            onChange={(value) => {
                                                                const recordStart =
                                                                    recordStartMap[index] ||
                                                                    'case_0_start';
                                                                const currentScene = sceneListMap[
                                                                    index
                                                                ].find((s) => s.id === scene.id);

                                                                // 验证策略选择是否合理
                                                                if (
                                                                    !validateStrategyOrder(
                                                                        recordStart,
                                                                        currentScene.firstFrameStrategy,
                                                                        value
                                                                    )
                                                                ) {
                                                                    message.warning(
                                                                        '尾帧策略不能早于首帧策略执行'
                                                                    );
                                                                    return;
                                                                }

                                                                const updatedScenes = sceneListMap[
                                                                    index
                                                                ].map((s) =>
                                                                    s.id === scene.id
                                                                        ? {
                                                                              ...s,
                                                                              lastFrameStrategy:
                                                                                  value
                                                                          }
                                                                        : s
                                                                );
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }}
                                                            options={[
                                                                ...(scene.firstFrameStrategy
                                                                    ? getAvailableLastFrameOptions(
                                                                          scene.firstFrameStrategy
                                                                      )
                                                                    : STRATEGY_OPTIONS.lastFrame),
                                                                ...getLastFrameOptionsFromSteps(
                                                                    stepInfo.stepInfo
                                                                )
                                                            ].filter(
                                                                (option, index, self) =>
                                                                    index ===
                                                                    self.findIndex(
                                                                        (o) =>
                                                                            o.value === option.value
                                                                    )
                                                            )}
                                                        />
                                                    </div>
                                                </Card>
                                            ))}

                                            {/* 添加场景按钮 */}
                                            <div style={{ marginBottom: '16px' }}>
                                                <Button
                                                    type="dashed"
                                                    block
                                                    icon={
                                                        <span style={{ marginRight: '4px' }}>
                                                            +
                                                        </span>
                                                    }
                                                    onClick={() => handleAddScene(index)}
                                                    style={{
                                                        borderColor: '#d9d9d9',
                                                        color: '#666666',
                                                        backgroundColor: '#fafafa',
                                                        borderRadius: '6px',
                                                        height: '40px',
                                                        fontSize: '14px'
                                                    }}
                                                >
                                                    添加场景
                                                </Button>
                                            </div>
                                        </Card>
                                    );
                                })}
                            </>
                        )}
                    </Form>
                </div>
                <CaseNodeTree
                    ref={caseNodeTreeModalRef}
                    caseNodeList={caseNodeList}
                    modalType="create"
                    setCaseNodeList={(e) => {
                        setCaseNodeList(e);
                    }}
                    filterType={[2]}
                />
            </CardContent>
        </Spin>
    );
}

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule,
    currentDevice: state.common.base.currentDevice
}))(CreatePlanPage);
