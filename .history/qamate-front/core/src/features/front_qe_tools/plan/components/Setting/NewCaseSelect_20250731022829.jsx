import { useState, useEffect } from 'react';
import { Tree, Input, Tooltip, Badge } from 'antd';
import { useLocation } from 'umi';
import { isEmpty } from 'lodash';
import { FilterOutlined, SearchOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import NoContent from 'COMMON/components/common/NoContent';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import { FILTER_DATA, filterObject } from 'COMMON/utils/commonUtils';
import NodeAccess from 'COMMON/components/case/NodeAccess';
import NodePriority from 'COMMON/components/case/NodePriority';
import NodeType from 'COMMON/components/case/NodeType';
import Filter from 'COMMON/components/Filter';
import { filterConditionCaseNodeRecursive } from 'COMMON/utils/commonUtils';
import { findNodeDetail } from 'COMMON/utils/treeUtils';
import { filterValidCase } from 'FEATURES/front_qe_tools/plan/utils';
import {
    convertOsTypeToType,
    filterNodesByOsType
} from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import styles from './common.module.less';

const { Search } = Input;

function CaseSelect(props) {
    const {
        caseNodeList,
        setCaseNodeList,
        curCaseRootId,
        curNodeId,
        curOsType,
        dataSource,
        disabled = false,
        filterType,
        initFilterData
    } = props;
    console.log('caseNodeList', caseNodeList,dataSource);
    const [selectKey, setSelectKey] = useState([]);
    const [searchDataSource, setSearchDataSource] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [showSearch, setShowSearch] = useState(false);
    const [showFilter, setShowFilter] = useState(false);
    const location = useLocation();
    const [filterData, setFilterData] = useState(
        filterObject(FILTER_DATA, ['stampResult', 'autoStatus'])
    );

    useEffect(() => {
        if (!isEmpty(initFilterData)) {
            setFilterData(initFilterData);
        }
    }, [initFilterData]);
    // 搜索过滤
    const filterTree = (arr, parentId, first = true, includesKeys = [], parentIds = []) => {
        if (first) {
            arr = JSON.parse(JSON.stringify(arr));
        }
        let emptyArr = [];
        for (let item of arr) {
            item.parentId = parentId ?? 0;
            parentIds.push(parentId ?? 0);
            if (!item?.nodeName || item?.nodeName === null) {
                item.nodeName = '--';
            }
            item.parentIds = [...parentIds];
            if (item?.nodeName?.includes(searchValue)) {
                includesKeys.push(item.caseNodeId);
                for (let _item of item.parentIds) {
                    if (!includesKeys.includes(_item)) {
                        includesKeys.push(_item);
                    }
                }
                emptyArr.push(item);
            } else if (!isEmpty(item.children)) {
                let res = filterTree(
                    item.children,
                    item.caseNodeId,
                    false,
                    includesKeys,
                    parentIds
                );
                item.children = res.data;
                includesKeys = res.includesKeys;
                if (item.children.length) {
                    emptyArr.push(item);
                }
            }
            parentIds.pop();
        }
        return { data: emptyArr, includesKeys: includesKeys };
    };

    const getSelectKey = (data, keys = []) => {
        for (let item of data) {
            // 选上一次执行的case
            if (
                filterType.includes('2') &&
                !keys.includes(item.caseNodeId) &&
                isEmpty(item.children)
            ) {
                if (
                    (plan && plan?.cloudPlanId === currentCloudPlan?.cloudPlanId) ||
                    sign?.type === 2
                ) {
                    keys.push(item.caseNodeId);
                }
            }
            // 选成功的case
            if (
                filterType.includes('3') &&
                !keys.includes(item.caseNodeId) &&
                isEmpty(item.children)
            ) {
                if (sign?.type === 2 && [1].includes(sign?.status)) {
                    keys.push(item.caseNodeId);
                }
            }
            // 选失败的case
            if (
                filterType.includes('4') &&
                !keys.includes(item.caseNodeId) &&
                isEmpty(item.children)
            ) {
                if (sign?.type === 2 && [2, 3].includes(sign?.status)) {
                    keys.push(item.caseNodeId);
                }
            }
            // 选异常的case
            if (
                filterType.includes('5') &&
                !keys.includes(item.caseNodeId) &&
                isEmpty(item.children)
            ) {
                if ([4].includes(plan?.status)) {
                    keys.push(item.caseNodeId);
                }
            }
            // 选待测
            if (
                filterType.includes('6') &&
                !keys.includes(item.caseNodeId) &&
                isEmpty(item.children)
            ) {
                // 取消/待测
                if ((sign?.type === 2 && [5].includes(sign?.status)) || !sign?.status) {
                    keys.push(item.caseNodeId);
                }
            }

            if (item.children && Array.isArray(item.children) && item.children.length > 0) {
                getSelectKey(item.children, keys);
            }
        }
        return keys;
    };

    const getChild = (child, checkedKeys) => {
        return child.map((item) => ({
            ...item,
            disabled: checkedKeys.includes(item.key),
            children: getChild(item.children, checkedKeys)
        }));
    };

    const getLeafNodeIds = (tree) => {
        let leafIds = [];
        if (isEmpty(tree.children)) {
            leafIds.push(tree.caseNodeId);
        } else {
            for (let node of tree.children) {
                leafIds.push(...getLeafNodeIds(node));
            }
        }
        return leafIds;
    };
    function findLeafNodesByIds(caseNodeIds, searchDataSource) {
        let leafNodes = [];
        const nodeMap = new Map();
        // 构建节点映射表
        function buildNodeMap(nodes) {
            nodes?.forEach((node) => {
                // 将节点存入 map，以 caseNodeId 为键
                nodeMap.set(node.caseNodeId, node);
                // 如果节点有子节点，递归处理子节点
                if (node.children && node.children.length > 0) {
                    buildNodeMap(node.children);
                }
            });
        }
        // 初始化节点映射表
        buildNodeMap(searchDataSource);
        // 遍历每个输入的 caseNodeId
        caseNodeIds?.forEach((id) => {
            // 从节点映射表中获取起始节点
            const startNode = nodeMap.get(id);
            if (startNode) {
                // 检查节点是否为叶子节点
                if (!startNode.children || startNode.children.length === 0) {
                    leafNodes.push(startNode.caseNodeId);
                } else {
                    // 对于有子节点的节点，开始递归收集叶子节点
                    startNode.children?.forEach((child) => traverseAndCollectLeaves(child));
                }
            }
        });
        // 递归遍历一个节点，收集所有叶子节点
        function traverseAndCollectLeaves(node) {
            if (!node.children || node.children.length === 0) {
                // 如果没有子节点，这是一个叶子节点
                leafNodes.push(node.caseNodeId);
            } else {
                // 递归处理子节点
                node.children?.forEach((child) => traverseAndCollectLeaves(child));
            }
        }
        return leafNodes;
    }
    useEffect(() => {
        let { data } = filterTree(dataSource, searchValue);
        setSearchDataSource(data);
    }, [searchValue]);

    useEffect(() => {
        if (filterType && location.pathname.includes('daily')) {
            const result = filterValidCase(
                dataSource,
                convertOsTypeToType(curOsType),
                [],
                filterType
            );
            // console.log(dataSource, filterType, result, curOsType, convertOsTypeToType(curOsType))
            setSearchDataSource(result);
        }
    }, [dataSource, filterType, curOsType]);
    useEffect(() => {
        let selectKey = getKeys(dataSource);
        let parentKey = caseNodeList?.find(
            (item) => item.nodeId === curNodeId && item.osType === curOsType
        )?.caseIdList;
        const leafNodeIds = findLeafNodesByIds(parentKey, searchDataSource);
        let finalSelectKey = leafNodeIds?.length
            ? leafNodeIds
            : caseNodeList?.find(
                  (item) => item.nodeId === curNodeId && item.osType === curOsType
              )?.caseIdList ?? [];
        finalSelectKey = [...new Set(finalSelectKey)];
        setSelectKey(finalSelectKey);
        // 只有初始化时才需要展开，如果已经展开了就不再处理
        if (!expandedKeys.length) {
            setExpandedKeys(selectKey);
        }
    }, [dataSource, expandedKeys]);
    const updateSelectKeys = (filterData) => {
        let newSelectKeys = [];
        let newCaseNodeList = [...caseNodeList];
        let caseIndex = caseNodeList.findIndex(
            (item) => item.caseRootId === curCaseRootId && curOsType === item.osType
        );
        let selectFlag = Object.keys(filterData).every((key) => {
            return isEmpty(filterData[key]?.data);
        });
        if (selectFlag) {
            newSelectKeys = [];
        } else {
            let _caseNodeWithCondition = filterConditionCaseNodeRecursive(dataSource, {
                search: searchValue,
                ...filterData,
                stampInfo: {
                    osType: curOsType // 端类型
                }
            });
            let childrenKeys = getLeafNodeIds(_caseNodeWithCondition?.[0] ?? {});
            newSelectKeys = childrenKeys ?? [];
        }
        if (caseIndex === -1) {
            newCaseNodeList.push({
                caseRootId: curCaseRootId,
                nodeId: curNodeId,
                osType: curOsType,
                caseIdList: newSelectKeys
            });
        } else {
            newCaseNodeList[caseIndex] = {
                nodeId: curNodeId,
                caseRootId: curCaseRootId,
                osType: curOsType,
                caseIdList: newSelectKeys
            };
        }
        setSelectKey(newSelectKeys);
        setCaseNodeList(newCaseNodeList);
    };

    const searchOnChange = (e) => {
        const { value } = e.target;
        setSearchValue(value);
    };

    const getKeys = (checkedNodes = [], keys = []) => {
        checkedNodes.forEach((item) => {
            if (!keys.includes(item.caseNodeId)) {
                keys.push(item.caseNodeId);
            }
            if (item.children && item.children.length > 0) {
                getKeys(item.children, keys);
            }
        });
        return keys;
    };

    const getChildrenKeys = (checkedNodes = [], keys = []) => {
        checkedNodes.forEach((item) => {
            if (item.children && item.children.length > 0) {
                getChildrenKeys(item.children, keys);
            } else {
                if (!keys.includes(item.caseNodeId)) {
                    keys.push(item.caseNodeId);
                }
            }
        });
        return keys;
    };
    const onCheck = (_, e) => {
        let checkedKey = [];
        if (e.checked) {
            let check = getChildrenKeys([e.node]);
            checkedKey = [...check, ...selectKey];
        } else {
            let check = getChildrenKeys([e.node]);

            // 如果在筛选状态下，需要特殊处理以避免误删除不在筛选结果中的节点
            if (searchValue) {
                // 获取当前节点的所有叶子节点
                const fullNode = findNodeDetail(dataSource, e.node.caseNodeId);
                const fullNodeLeafKeys = fullNode ? getChildrenKeys([fullNode]) : [];
                // 移除那些在筛选结果中出现的叶子节点
                checkedKey = selectKey.filter((item) => {
                    if (!fullNodeLeafKeys.includes(item)) {
                        return true;
                    }
                    return !check.includes(item);
                });
            } else {
                checkedKey = selectKey.filter((item) => !check.includes(item));
            }
        }
        checkedKey = [...new Set(checkedKey)];
        setSelectKey(checkedKey);

        let newCaseNodeList = [...caseNodeList];
        let caseIndex = caseNodeList.findIndex(
            (item) => item.caseRootId === curCaseRootId && curOsType === item.osType
        );

        if (caseIndex === -1) {
            newCaseNodeList.unshift({
                caseRootId: curCaseRootId,
                nodeId: curNodeId,
                osType: curOsType,
                caseIdList: checkedKey
            });
        } else {
            newCaseNodeList[caseIndex] = {
                nodeId: curNodeId,
                caseRootId: curCaseRootId,
                osType: curOsType,
                caseIdList: checkedKey
            };
        }

        setCaseNodeList(newCaseNodeList);
    };

    const onExpand = (expandedKeysValue) => {
        setExpandedKeys(expandedKeysValue);
    };

    return (
        <div>
            <div className={styles.operaGroup}>
                <Tooltip title="搜索">
                    <span
                        className={classnames(styles.filterOpera, {
                            [styles.filterOperaActived]: showSearch
                        })}
                        onClick={() => {
                            setShowSearch(!showSearch);
                            if (!showSearch) {
                                setShowFilter(false);
                            }
                        }}
                    >
                        <Badge dot={searchValue !== ''} size="small">
                            <SearchOutlined className={styles.icon} />
                        </Badge>
                    </span>
                </Tooltip>
                <Filter
                    type="check"
                    title="默认勾选"
                    onChange={() => setShowFilter(false)}
                    caseNode={dataSource}
                    filterData={filterData}
                    handleFilterData={(value, obj, activeKey) => {
                        let newFilterData = {
                            ...filterData,
                            [obj]: {
                                activeKey: activeKey ?? filterData[obj]?.activeKey,
                                data: value
                            }
                        };
                        setFilterData(newFilterData);
                        updateSelectKeys(newFilterData);
                    }}
                    clearFilterData={(data) => {
                        setFilterData(data);
                        updateSelectKeys(data);
                    }}
                >
                    <Tooltip title="快速勾选用例">
                        <span
                            className={classnames(styles.filterOpera, {
                                [styles.filterOperaActived]: showFilter
                            })}
                        >
                            <Badge
                                dot={Object.values(filterData).some(
                                    (value) => !isEmpty(value?.data)
                                )} // 一个条件为非空.true
                                size="small"
                            >
                                <FilterOutlined className={styles.icon} />
                            </Badge>
                        </span>
                    </Tooltip>
                </Filter>
                <div className={styles.operaInput}>
                    {showSearch && (
                        <Search
                            size="small"
                            className={styles.input}
                            placeholder="关键词"
                            value={searchValue}
                            onChange={(e) => searchOnChange(e)}
                        />
                    )}
                </div>
            </div>

            <div style={{ overflow: 'auto', height: window.innerHeight * 0.5 }}>
                {searchDataSource.length === 0 ? (
                    <NoContent text="无对应自动化用例" className={styles.noContent} />
                ) : (
                    <Tree
                        checkable
                        showIcon
                        showLine
                        defaultExpandAll
                        height={window.innerHeight * 0.5}
                        disabled={disabled}
                        expandedKeys={expandedKeys}
                        onExpand={onExpand}
                        onCheck={onCheck}
                        fieldNames={{
                            key: 'caseNodeId',
                            title: 'nodeName'
                        }}
                        treeData={filterNodesByOsType(searchDataSource, curOsType)}
                        checkedKeys={selectKey || []}
                        titleRender={(node) => {
                            return (
                                <div className={styles.nodeTitle}>
                                    <NodeAccess node={node} />
                                    <NodePriority node={node} />
                                    <span className={styles.nodeTextTitle}>{node.nodeName}</span>
                                    <NodeType node={node} curOsType={curOsType} />
                                </div>
                            );
                        }}
                    />
                )}
            </div>
        </div>
    );
}

export default connectModel([baseModel, planModel], (state) => ({
    creationConfig: state.common.base.creationConfig,
    tagList: state.common.base.tagList,
    planList: state.common.plan.list
}))(CaseSelect);
