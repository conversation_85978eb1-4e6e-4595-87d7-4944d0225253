import {isEmpty} from 'lodash';

// 获取树的端类型
export function getTreeWithOs(tree, treeNodeList) {
    return tree.map(node => {
        // nodeType: 1 目录 2 用例
        // 1-android 2-ios 3-common(双端复用) 4-web 5-server 6-os隔离
        let nodeDetail = treeNodeList.find(item => item.treeNodeId === node.nodeId);
        if (node.nodeType === 2) {
            let _child = [];
            nodeDetail.taskList.forEach(task => {
                _child.push({
                    nodeId: node.nodeId + '-' + task.osType,
                    os: task.osType
                });
            });
            node.children = _child;
        } else {
            getTreeWithOs(node.children, treeNodeList);
        }
    });
}

// 过滤出自动化/半自动用例

export function filterValidCase(list, osType, executeTypeList = [], filterType = [2, 4]) {
    console.log('filterValidCase 调用:', { list, osType, executeTypeList, filterType });
    try {
        let newData = [];
        list.forEach((element, index) => {
            console.log(`处理节点 ${element?.caseNodeId || element?.nodeId}:`, {
                nodeName: element?.nodeName,
                disabled: element?.extra?.disabled,
                disabledInfo: element?.extra?.disabledInfo?.[osType]?.status,
                executionType: element?.extra?.executionType?.[osType],
                hasChildren: !isEmpty(element.children)
            });

            // 过滤掉禁用用例
            if (element?.extra?.disabled ?? element?.extra?.disabledInfo?.[osType]?.status) {
                console.log('节点被禁用，跳过');
                return;
            }
            executeTypeList.push(element?.extra?.executionType?.[osType]);
            if (!isEmpty(element.children) && element?.extra?.executionType?.[osType] !== 1) {
                let _child = filterValidCase(element.children, osType, executeTypeList, filterType);
                if (_child.length > 0) {
                    newData.push({
                        ...element,
                        children: _child
                    });
                }
            } else {
                let nodeType = 1;
                if (executeTypeList[executeTypeList.length - 1] === 0 || executeTypeList.includes(1)) {
                    nodeType = 1;
                } else if (executeTypeList.includes(4)) {
                    nodeType = 4;
                } else {
                    nodeType = 2;
                }
                console.log(`叶子节点判断:`, {
                    executeTypeList,
                    currentExecutionType: executeTypeList[executeTypeList.length - 1],
                    nodeType,
                    filterType,
                    shouldInclude: filterType?.includes(nodeType)
                });
                if (filterType?.includes(nodeType)) {
                    console.log('节点通过过滤，添加到结果');
                    newData.push(element);
                } else {
                    console.log('节点未通过过滤，跳过');
                }
            }
            executeTypeList.pop();
        });
        console.log('filterValidCase 结果:', newData);
        return newData;
    } catch (err) {
        console.log('filterValidCase 错误:', err);
        return [];
    }
};